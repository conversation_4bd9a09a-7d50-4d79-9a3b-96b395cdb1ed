"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearTestDataOnly = clearTestDataOnly;
const typeorm_1 = require("typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../../users/entities/user.entity");
const job_entity_1 = require("../../jobs/entities/job.entity");
const job_template_entity_1 = require("../../jobs/entities/job-template.entity");
const application_entity_1 = require("../../applications/entities/application.entity");
const contact_request_entity_1 = require("../../chat/entities/contact-request.entity");
const conversation_entity_1 = require("../../chat/entities/conversation.entity");
const chat_message_entity_1 = require("../../chat/entities/chat-message.entity");
const notification_entity_1 = require("../../notifications/entities/notification.entity");
async function clearTestDataOnly() {
    const configService = new config_1.ConfigService();
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'postgres123'),
        database: configService.get('DB_DATABASE', 'job_platform'),
        entities: [user_entity_1.User, job_entity_1.Job, job_template_entity_1.JobTemplate, application_entity_1.Application, contact_request_entity_1.ContactRequest, conversation_entity_1.Conversation, chat_message_entity_1.ChatMessage, notification_entity_1.Notification],
        synchronize: false,
    });
    try {
        await dataSource.initialize();
        console.log('📦 Database connection established for test data cleanup');
        console.log('🧹 Clearing test data only...');
        const testUserEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        const testUsers = await dataSource
            .createQueryBuilder()
            .select('user.id')
            .from(user_entity_1.User, 'user')
            .where('user.email IN (:...emails)', { emails: testUserEmails })
            .orWhere('user.firebaseUid IN (:...uids)', {
            uids: ['user1', 'user2', 'contact-firebase-uid', 'journey-firebase-uid', 'requester-firebase-uid', 'sync-firebase-uid', 'test-firebase-uid']
        })
            .getMany();
        const testUserIds = testUsers.map(user => user.id);
        console.log(`🎯 Found ${testUserIds.length} test users to clean up`);
        if (testUserIds.length > 0) {
            await dataSource.createQueryBuilder()
                .delete()
                .from(notification_entity_1.Notification)
                .where('userId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(chat_message_entity_1.ChatMessage)
                .where('senderId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(conversation_entity_1.Conversation)
                .where('user1Id IN (:...userIds) OR user2Id IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(contact_request_entity_1.ContactRequest)
                .where('requesterId IN (:...userIds) OR jobPosterId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(application_entity_1.Application)
                .where('applicantId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(job_template_entity_1.JobTemplate)
                .where('userId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(job_entity_1.Job)
                .where('employerId IN (:...userIds)', { userIds: testUserIds })
                .execute();
            await dataSource.createQueryBuilder()
                .delete()
                .from(user_entity_1.User)
                .where('id IN (:...userIds)', { userIds: testUserIds })
                .execute();
        }
        const testJobTitles = [
            'Test Job%',
            'Journey Test%',
            '%Test%Contact%',
            '%Test%Accepted%',
            '%Software Developer%',
            '%Test%',
            '%test%',
            '%Contact Request%',
            '%Firebase%',
            '%Mock%',
            '%Demo%',
            '%Sample%'
        ];
        for (const titlePattern of testJobTitles) {
            const result = await dataSource.createQueryBuilder()
                .delete()
                .from(job_entity_1.Job)
                .where('title LIKE :pattern', { pattern: titlePattern })
                .execute();
            if (result.affected && result.affected > 0) {
                console.log(`🗑️ Deleted ${result.affected} jobs matching pattern: ${titlePattern}`);
            }
        }
        const testDescriptionPatterns = [
            '%test job%',
            '%Test job%',
            '%verify%',
            '%functionality%',
            '%Great opportunity%',
            '%This is a test%'
        ];
        for (const descPattern of testDescriptionPatterns) {
            const result = await dataSource.createQueryBuilder()
                .delete()
                .from(job_entity_1.Job)
                .where('description LIKE :pattern', { pattern: descPattern })
                .execute();
            if (result.affected && result.affected > 0) {
                console.log(`🗑️ Deleted ${result.affected} jobs with test description pattern: ${descPattern}`);
            }
        }
        const testLocationPatterns = [
            '%Test Location%',
            '%San Francisco%',
            '%Buenos Aires%'
        ];
        for (const locationPattern of testLocationPatterns) {
            const result = await dataSource.createQueryBuilder()
                .delete()
                .from(job_entity_1.Job)
                .where('location LIKE :pattern', { pattern: locationPattern })
                .execute();
            if (result.affected && result.affected > 0) {
                console.log(`🗑️ Deleted ${result.affected} jobs with test location pattern: ${locationPattern}`);
            }
        }
        console.log('✅ Test data cleared successfully');
    }
    catch (error) {
        console.error('❌ Error clearing test data:', error);
    }
    finally {
        await dataSource.destroy();
    }
}
if (require.main === module) {
    clearTestDataOnly();
}
//# sourceMappingURL=clear-database.js.map