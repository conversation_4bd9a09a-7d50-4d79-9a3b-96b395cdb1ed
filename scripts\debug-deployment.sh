#!/bin/bash

# ChanguApp Deployment Debug Script
# =================================
# This script helps debug deployment issues on EC2

echo "🔍 ChanguApp Deployment Debug Report"
echo "===================================="
echo "Timestamp: $(date)"
echo ""

# System Information
echo "📊 System Information:"
echo "  OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "  Uptime: $(uptime)"
echo "  Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "  Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
echo ""

# Docker Status
echo "🐳 Docker Status:"
if command -v docker &> /dev/null; then
    echo "  Docker version: $(docker --version)"
    echo "  Docker status: $(sudo systemctl is-active docker)"
    echo "  Docker Compose version: $(docker-compose --version 2>/dev/null || echo 'Not installed')"
    echo ""
    
    echo "  Running containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "    No containers running"
    echo ""
    
    echo "  Docker system info:"
    docker system df 2>/dev/null || echo "    Docker system info unavailable"
else
    echo "  Docker: Not installed"
fi
echo ""

# Port Status
echo "🔌 Port Status:"
echo "  Port 3000 (Backend):"
sudo netstat -tlnp | grep :3000 || echo "    Not listening"
echo "  Port 5432 (PostgreSQL):"
sudo netstat -tlnp | grep :5432 || echo "    Not listening"
echo "  Port 80 (Nginx):"
sudo netstat -tlnp | grep :80 || echo "    Not listening"
echo ""

# Service Status
echo "🔧 Service Status:"
echo "  Nginx: $(sudo systemctl is-active nginx 2>/dev/null || echo 'inactive')"
echo "  PostgreSQL: $(sudo systemctl is-active postgresql 2>/dev/null || echo 'inactive')"
echo ""

# Application Status
echo "🚀 Application Status:"
if [ -d "/home/<USER>/tuchanga/backend" ]; then
    cd /home/<USER>/tuchanga/backend
    echo "  Backend directory: ✅ Found"
    
    if [ -f "docker-compose.yml" ]; then
        echo "  Docker Compose config: ✅ Found"
        echo ""
        echo "  Docker Compose services:"
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps 2>/dev/null || echo "    Services not running"
        echo ""
        
        echo "  Recent backend logs:"
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs backend --tail=5 2>/dev/null || echo "    No backend logs"
        echo ""
        
        echo "  Recent postgres logs:"
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs postgres --tail=3 2>/dev/null || echo "    No postgres logs"
    else
        echo "  Docker Compose config: ❌ Not found"
    fi
else
    echo "  Backend directory: ❌ Not found"
fi
echo ""

# Health Checks
echo "🔍 Health Checks:"
echo "  Backend health (local):"
if curl -f http://localhost:3000/health 2>/dev/null; then
    echo "    ✅ Backend responding"
else
    echo "    ❌ Backend not responding"
fi

echo "  Frontend (local):"
if curl -f http://localhost/ 2>/dev/null; then
    echo "    ✅ Frontend responding"
else
    echo "    ❌ Frontend not responding"
fi
echo ""

# Process Information
echo "🔄 Process Information:"
echo "  Node.js processes:"
ps aux | grep node | grep -v grep || echo "    No Node.js processes"
echo ""
echo "  Docker processes:"
ps aux | grep docker | grep -v grep || echo "    No Docker processes"
echo ""

# Recent System Logs
echo "📋 Recent System Logs (last 10 lines):"
sudo journalctl --no-pager -n 10 | tail -10
echo ""

echo "🔍 Debug report completed at $(date)"
echo "===================================="
