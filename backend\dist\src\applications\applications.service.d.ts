import { Application } from './entities/application.entity';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
export declare class ApplicationsService {
    private applications;
    constructor();
    create(createApplicationDto: CreateApplicationDto, applicantId: string): Promise<Application>;
    findAll(): Promise<Application[]>;
    findOne(id: string): Promise<Application>;
    findByJob(jobId: string): Promise<Application[]>;
    findByApplicant(applicantId: string): Promise<Application[]>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, userId: string): Promise<Application>;
    withdraw(id: string, userId: string): Promise<Application>;
    remove(id: string, userId: string): Promise<void>;
}
