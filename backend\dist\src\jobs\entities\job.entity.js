"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Job = exports.PaymentMethod = exports.JobCategory = exports.JobStatus = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("../../users/entities/user.entity");
const application_entity_1 = require("../../applications/entities/application.entity");
var JobStatus;
(function (JobStatus) {
    JobStatus["OPEN"] = "open";
    JobStatus["IN_PROGRESS"] = "in_progress";
    JobStatus["COMPLETED"] = "completed";
    JobStatus["CANCELLED"] = "cancelled";
    JobStatus["PAUSED"] = "paused";
})(JobStatus || (exports.JobStatus = JobStatus = {}));
var JobCategory;
(function (JobCategory) {
    JobCategory["HOME_MAINTENANCE"] = "home_maintenance";
    JobCategory["GARDENING"] = "gardening";
    JobCategory["CLEANING"] = "cleaning";
    JobCategory["PAINTING"] = "painting";
    JobCategory["PLUMBING"] = "plumbing";
    JobCategory["ELECTRICAL"] = "electrical";
    JobCategory["MOVING"] = "moving";
    JobCategory["DELIVERY"] = "delivery";
    JobCategory["TUTORING"] = "tutoring";
    JobCategory["PET_CARE"] = "pet_care";
    JobCategory["CHILDCARE"] = "childcare";
    JobCategory["COOKING"] = "cooking";
    JobCategory["TECH_SUPPORT"] = "tech_support";
    JobCategory["HANDYMAN"] = "handyman";
    JobCategory["CARPENTRY"] = "carpentry";
    JobCategory["ELDERLY_CARE"] = "elderly_care";
    JobCategory["OTHER"] = "other";
})(JobCategory || (exports.JobCategory = JobCategory = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CASH"] = "cash";
    PaymentMethod["BANK_TRANSFER"] = "bank_transfer";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
let Job = class Job {
    id;
    title;
    description;
    category;
    budget;
    location;
    latitude;
    longitude;
    status;
    requiredSkills;
    estimatedHours;
    startDate;
    deadline;
    isUrgent;
    images;
    icon;
    paymentMethods;
    createdAt;
    updatedAt;
    deletedAt;
    employer;
    employerId;
    completedBy;
    completedById;
    completedAt;
    applications;
};
exports.Job = Job;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job unique identifier' }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Job.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job title' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Job.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job description' }),
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], Job.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job category', enum: JobCategory }),
    (0, typeorm_1.Column)({
        type: 'varchar',
    }),
    __metadata("design:type", String)
], Job.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job budget/payment amount' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Job.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job location address' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Job.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job latitude for location-based search' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 8 }),
    __metadata("design:type", Number)
], Job.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job longitude for location-based search' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 11, scale: 8 }),
    __metadata("design:type", Number)
], Job.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job status', enum: JobStatus }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: JobStatus.OPEN,
    }),
    __metadata("design:type", String)
], Job.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Required skills for the job', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Job.prototype, "requiredSkills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Estimated duration in hours', required: false }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Job.prototype, "estimatedHours", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job start date', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Job.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job deadline', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Job.prototype, "deadline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether job is urgent' }),
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Job.prototype, "isUrgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job images URLs', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Job.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job icon emoji for visual representation', required: false }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 10, nullable: true }),
    __metadata("design:type", String)
], Job.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Accepted payment methods for the job', enum: PaymentMethod, isArray: true }),
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], Job.prototype, "paymentMethods", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job creation date' }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Job.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job last update date' }),
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Job.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job deletion date (soft delete)', required: false }),
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Job.prototype, "deletedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job employer', type: () => user_entity_1.User }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.postedJobs),
    (0, typeorm_1.JoinColumn)({ name: 'employerId' }),
    __metadata("design:type", user_entity_1.User)
], Job.prototype, "employer", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Job.prototype, "employerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User who completed the job', type: () => user_entity_1.User, required: false }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'completedById' }),
    __metadata("design:type", user_entity_1.User)
], Job.prototype, "completedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Job.prototype, "completedById", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job completion date', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Job.prototype, "completedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job applications', type: () => [application_entity_1.Application] }),
    (0, typeorm_1.OneToMany)(() => application_entity_1.Application, (application) => application.job),
    __metadata("design:type", Array)
], Job.prototype, "applications", void 0);
exports.Job = Job = __decorate([
    (0, typeorm_1.Entity)('jobs')
], Job);
//# sourceMappingURL=job.entity.js.map