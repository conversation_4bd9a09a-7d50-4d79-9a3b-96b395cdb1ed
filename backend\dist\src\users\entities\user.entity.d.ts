import { Job } from '../../jobs/entities/job.entity';
import { Application } from '../../applications/entities/application.entity';
import { JobTemplate } from '../../jobs/entities/job-template.entity';
export declare enum UserRole {
    EMPLOYER = "employer",
    EMPLOYEE = "employee",
    BOTH = "both"
}
export declare class User {
    id: string;
    email: string;
    firebaseUid: string;
    firstName: string;
    lastName: string;
    phone?: string;
    role: UserRole;
    profilePicture?: string;
    bio?: string;
    skills?: string;
    location?: string;
    latitude?: number;
    longitude?: number;
    hourlyRate?: number;
    isAvailable: boolean;
    isVip: boolean;
    completedJobs: number;
    postedJobsCompleted: number;
    createdAt: Date;
    updatedAt: Date;
    postedJobs: Job[];
    applications: Application[];
    jobTemplates: JobTemplate[];
}
