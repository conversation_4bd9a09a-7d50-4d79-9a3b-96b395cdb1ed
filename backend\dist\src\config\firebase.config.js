"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.firebaseAdmin = void 0;
const admin = require("firebase-admin");
const firebaseConfig = {
    projectId: process.env.FIREBASE_PROJECT_ID || "tu-changa-583b3",
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
};
if (!admin.apps || !admin.apps.length) {
    try {
        if (firebaseConfig.privateKey && firebaseConfig.clientEmail) {
            admin.initializeApp({
                credential: admin.credential.cert({
                    projectId: firebaseConfig.projectId,
                    privateKey: firebaseConfig.privateKey,
                    clientEmail: firebaseConfig.clientEmail,
                }),
            });
            console.log('✅ Firebase Admin SDK initialized with service account credentials');
        }
        else {
            admin.initializeApp({
                projectId: firebaseConfig.projectId,
            });
            console.log('✅ Firebase Admin SDK initialized with application default credentials');
        }
    }
    catch (error) {
        console.error('❌ Firebase Admin SDK initialization failed:', error.message);
        console.log('📝 Please ensure you have proper Firebase credentials configured:');
        console.log('   - Set FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL environment variables');
        console.log('   - Or run "gcloud auth application-default login" for local development');
        throw new Error('Firebase Admin SDK initialization failed. Authentication will not work.');
    }
}
exports.firebaseAdmin = admin;
exports.default = admin;
//# sourceMappingURL=firebase.config.js.map