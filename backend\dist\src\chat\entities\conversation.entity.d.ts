import { User } from '../../users/entities/user.entity';
import { Job } from '../../jobs/entities/job.entity';
import { ChatMessage } from './chat-message.entity';
export declare class Conversation {
    id: string;
    user1: User;
    user1Id: string;
    user2: User;
    user2Id: string;
    job: Job;
    jobId: string;
    messages: ChatMessage[];
    lastMessageAt: Date;
    lastMessagePreview: string;
    unreadCountUser1: number;
    unreadCountUser2: number;
    isClosed: boolean;
    closedByUserId?: string;
    closedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
