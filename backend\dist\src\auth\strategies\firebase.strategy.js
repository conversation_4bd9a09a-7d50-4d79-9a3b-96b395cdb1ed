"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FirebaseStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirebaseStrategy = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const admin = require("firebase-admin");
let FirebaseStrategy = FirebaseStrategy_1 = class FirebaseStrategy {
    configService;
    defaultApp;
    logger = new common_1.Logger(FirebaseStrategy_1.name);
    isDevelopment;
    constructor(configService) {
        this.configService = configService;
        this.isDevelopment = configService.get('NODE_ENV') !== 'production';
        if (this.isDevelopment && !this.hasFirebaseCredentials()) {
            this.logger.warn('Firebase credentials not found. Running in development mode without Firebase.');
            return;
        }
        if (!admin.apps.length) {
            try {
                this.defaultApp = admin.initializeApp({
                    credential: admin.credential.cert({
                        projectId: configService.get('FIREBASE_PROJECT_ID'),
                        privateKey: configService.get('FIREBASE_PRIVATE_KEY')?.replace(/\\n/g, '\n'),
                        clientEmail: configService.get('FIREBASE_CLIENT_EMAIL'),
                    }),
                });
                this.logger.log('Firebase Admin SDK initialized successfully');
            }
            catch (error) {
                if (this.isDevelopment) {
                    this.logger.warn('Failed to initialize Firebase in development mode. Continuing without Firebase.');
                }
                else {
                    throw error;
                }
            }
        }
        else {
            this.defaultApp = admin.app();
        }
    }
    hasFirebaseCredentials() {
        const projectId = this.configService.get('FIREBASE_PROJECT_ID');
        const privateKey = this.configService.get('FIREBASE_PRIVATE_KEY');
        const clientEmail = this.configService.get('FIREBASE_CLIENT_EMAIL');
        return !!(projectId && privateKey && clientEmail);
    }
    async validate(token) {
        if (this.isDevelopment && !this.defaultApp) {
            this.logger.debug('Using mock authentication in development mode');
            return {
                uid: 'dev-user-123',
                email: '<EMAIL>',
                emailVerified: true,
            };
        }
        try {
            const decodedToken = await this.defaultApp.auth().verifyIdToken(token);
            return {
                uid: decodedToken.uid,
                email: decodedToken.email,
                emailVerified: decodedToken.email_verified,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid Firebase token');
        }
    }
};
exports.FirebaseStrategy = FirebaseStrategy;
exports.FirebaseStrategy = FirebaseStrategy = FirebaseStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], FirebaseStrategy);
//# sourceMappingURL=firebase.strategy.js.map