"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTestDatabase = setupTestDatabase;
const typeorm_1 = require("typeorm");
const config_1 = require("@nestjs/config");
async function setupTestDatabase() {
    const configService = new config_1.ConfigService();
    console.log('🧪 Setting up test database...');
    const adminDataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'postgres123'),
        database: 'postgres',
    });
    try {
        await adminDataSource.initialize();
        console.log('📦 Connected to PostgreSQL server');
        const result = await adminDataSource.query("SELECT 1 FROM pg_database WHERE datname = 'job_platform_test'");
        if (result.length === 0) {
            await adminDataSource.query('CREATE DATABASE job_platform_test');
            console.log('✅ Test database created: job_platform_test');
        }
        else {
            console.log('✅ Test database already exists: job_platform_test');
        }
    }
    catch (error) {
        console.error('❌ Error setting up test database:', error);
        throw error;
    }
    finally {
        await adminDataSource.destroy();
    }
}
if (require.main === module) {
    setupTestDatabase()
        .then(() => {
        console.log('🎉 Test database setup completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Test database setup failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=setup-test-db.js.map