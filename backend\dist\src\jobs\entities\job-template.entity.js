"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobTemplate = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("../../users/entities/user.entity");
const job_entity_1 = require("./job.entity");
let JobTemplate = class JobTemplate {
    id;
    title;
    description;
    category;
    budget;
    location;
    latitude;
    longitude;
    requiredSkills;
    estimatedHours;
    isUrgent;
    images;
    icon;
    paymentMethods;
    createdAt;
    user;
    userId;
};
exports.JobTemplate = JobTemplate;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Template unique identifier' }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], JobTemplate.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job title from template' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], JobTemplate.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job description from template' }),
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], JobTemplate.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job category from template', enum: job_entity_1.JobCategory }),
    (0, typeorm_1.Column)({
        type: 'varchar',
    }),
    __metadata("design:type", String)
], JobTemplate.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job budget from template' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], JobTemplate.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job location from template' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], JobTemplate.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job latitude from template' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 8 }),
    __metadata("design:type", Number)
], JobTemplate.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job longitude from template' }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 11, scale: 8 }),
    __metadata("design:type", Number)
], JobTemplate.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Required skills from template', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], JobTemplate.prototype, "requiredSkills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Estimated duration in hours from template', required: false }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], JobTemplate.prototype, "estimatedHours", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether job was urgent in template' }),
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], JobTemplate.prototype, "isUrgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job images URLs from template', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], JobTemplate.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job icon emoji from template', required: false }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 10, nullable: true }),
    __metadata("design:type", String)
], JobTemplate.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Accepted payment methods from template', enum: job_entity_1.PaymentMethod, isArray: true }),
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], JobTemplate.prototype, "paymentMethods", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Template creation date' }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], JobTemplate.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Template owner', type: () => user_entity_1.User }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.jobTemplates),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], JobTemplate.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], JobTemplate.prototype, "userId", void 0);
exports.JobTemplate = JobTemplate = __decorate([
    (0, typeorm_1.Entity)('job_templates')
], JobTemplate);
//# sourceMappingURL=job-template.entity.js.map