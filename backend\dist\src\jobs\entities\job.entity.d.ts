import { User } from '../../users/entities/user.entity';
import { Application } from '../../applications/entities/application.entity';
export declare enum JobStatus {
    OPEN = "open",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    PAUSED = "paused"
}
export declare enum JobCategory {
    HOME_MAINTENANCE = "home_maintenance",
    GARDENING = "gardening",
    CLEANING = "cleaning",
    PAINTING = "painting",
    PLUMBING = "plumbing",
    ELECTRICAL = "electrical",
    MOVING = "moving",
    DELIVERY = "delivery",
    TUTORING = "tutoring",
    PET_CARE = "pet_care",
    CHILDCARE = "childcare",
    COOKING = "cooking",
    TECH_SUPPORT = "tech_support",
    HANDYMAN = "handyman",
    CARPENTRY = "carpentry",
    ELDERLY_CARE = "elderly_care",
    OTHER = "other"
}
export declare enum PaymentMethod {
    CASH = "cash",
    BANK_TRANSFER = "bank_transfer"
}
export declare class Job {
    id: string;
    title: string;
    description: string;
    category: JobCategory;
    budget: number;
    location: string;
    latitude: number;
    longitude: number;
    status: JobStatus;
    requiredSkills?: string;
    estimatedHours?: number;
    startDate?: Date;
    deadline?: Date;
    isUrgent: boolean;
    images?: string;
    icon?: string;
    paymentMethods: PaymentMethod[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    employer: User;
    employerId: string;
    completedBy?: User;
    completedById?: string;
    completedAt?: Date;
    applications: Application[];
}
