import { User } from '../../users/entities/user.entity';
import { Job } from '../../jobs/entities/job.entity';
export declare enum ContactRequestStatus {
    PENDING = "pending",
    ACCEPTED = "accepted",
    REJECTED = "rejected"
}
export declare class ContactRequest {
    id: string;
    requester: User;
    requesterId: string;
    jobPoster: User;
    jobPosterId: string;
    job: Job;
    jobId: string;
    status: ContactRequestStatus;
    message: string;
    createdAt: Date;
    updatedAt: Date;
}
