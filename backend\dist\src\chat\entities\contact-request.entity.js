"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactRequest = exports.ContactRequestStatus = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const job_entity_1 = require("../../jobs/entities/job.entity");
var ContactRequestStatus;
(function (ContactRequestStatus) {
    ContactRequestStatus["PENDING"] = "pending";
    ContactRequestStatus["ACCEPTED"] = "accepted";
    ContactRequestStatus["REJECTED"] = "rejected";
})(ContactRequestStatus || (exports.ContactRequestStatus = ContactRequestStatus = {}));
let ContactRequest = class ContactRequest {
    id;
    requester;
    requesterId;
    jobPoster;
    jobPosterId;
    job;
    jobId;
    status;
    message;
    createdAt;
    updatedAt;
};
exports.ContactRequest = ContactRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ContactRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'requester_id' }),
    __metadata("design:type", user_entity_1.User)
], ContactRequest.prototype, "requester", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'requester_id' }),
    __metadata("design:type", String)
], ContactRequest.prototype, "requesterId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'job_poster_id' }),
    __metadata("design:type", user_entity_1.User)
], ContactRequest.prototype, "jobPoster", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'job_poster_id' }),
    __metadata("design:type", String)
], ContactRequest.prototype, "jobPosterId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => job_entity_1.Job, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'job_id' }),
    __metadata("design:type", job_entity_1.Job)
], ContactRequest.prototype, "job", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'job_id' }),
    __metadata("design:type", String)
], ContactRequest.prototype, "jobId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: ContactRequestStatus.PENDING
    }),
    __metadata("design:type", String)
], ContactRequest.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ContactRequest.prototype, "message", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], ContactRequest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], ContactRequest.prototype, "updatedAt", void 0);
exports.ContactRequest = ContactRequest = __decorate([
    (0, typeorm_1.Entity)('contact_requests')
], ContactRequest);
//# sourceMappingURL=contact-request.entity.js.map