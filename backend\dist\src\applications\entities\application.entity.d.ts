import { User } from '../../users/entities/user.entity';
import { Job } from '../../jobs/entities/job.entity';
export declare enum ApplicationStatus {
    PENDING = "pending",
    ACCEPTED = "accepted",
    REJECTED = "rejected",
    WITHDRAWN = "withdrawn"
}
export declare class Application {
    id: string;
    message: string;
    proposedRate?: number;
    estimatedHours?: number;
    status: ApplicationStatus;
    employerResponse?: string;
    createdAt: Date;
    updatedAt: Date;
    applicant: User;
    applicantId: string;
    job: Job;
    jobId: string;
}
