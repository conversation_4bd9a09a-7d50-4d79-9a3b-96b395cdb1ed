name: ChanguApp EC2 Simple Deployment

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# Configuration is loaded dynamically from config/aws-config.env

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      deploy-frontend: ${{ steps.check.outputs.deploy-frontend }}
      deploy-backend: ${{ steps.check.outputs.deploy-backend }}
      should-deploy: ${{ steps.check.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check deployment flags
        id: check
        run: |
          COMMIT_MESSAGE="${{ github.event.head_commit.message }}"
          SHOULD_DEPLOY=false

          if [[ "$COMMIT_MESSAGE" == *"--frontend-deploy"* ]]; then
            echo "deploy-frontend=true" >> $GITHUB_OUTPUT
            echo "🎨 Frontend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-frontend=false" >> $GITHUB_OUTPUT
          fi

          if [[ "$COMMIT_MESSAGE" == *"--backend-deploy"* ]]; then
            echo "deploy-backend=true" >> $GITHUB_OUTPUT
            echo "🔧 Backend deployment requested"
            SHOULD_DEPLOY=true
          else
            echo "deploy-backend=false" >> $GITHUB_OUTPUT
          fi

          echo "should-deploy=$SHOULD_DEPLOY" >> $GITHUB_OUTPUT

  deploy-to-ec2:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Load AWS Configuration
        run: |
          echo "📋 Loading AWS configuration..."
          source config/load-aws-config.sh
          echo "EC2_HOST=$EC2_HOST" >> $GITHUB_ENV
          echo "EC2_PUBLIC_IP=$EC2_PUBLIC_IP" >> $GITHUB_ENV
          echo "EC2_USER=$EC2_USER" >> $GITHUB_ENV
          echo "SSH_KEY_PATH=$SSH_KEY_PATH" >> $GITHUB_ENV
          echo "APP_DEPLOY_DIR=$APP_DEPLOY_DIR" >> $GITHUB_ENV
          echo "APP_WEB_DIR=$APP_WEB_DIR" >> $GITHUB_ENV
          echo "APP_ENV_FILE=$APP_ENV_FILE" >> $GITHUB_ENV
          echo "FRONTEND_URL=$FRONTEND_URL" >> $GITHUB_ENV
          echo "BACKEND_URL=$BACKEND_URL" >> $GITHUB_ENV
          echo "HEALTH_URL=$HEALTH_URL" >> $GITHUB_ENV

      - name: Setup SSH Key
        run: |
          echo "🔑 Setting up SSH key..."
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_SSH_KEY }}" > ~/.ssh/tuchanga.pem
          chmod 600 ~/.ssh/tuchanga.pem
          ssh-keyscan -H $EC2_HOST >> ~/.ssh/known_hosts

          # Set SSH connection parameters as environment variables
          echo "SSH_OPTS=-i ~/.ssh/tuchanga.pem -o ConnectTimeout=30 -o StrictHostKeyChecking=no -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -o TCPKeepAlive=yes" >> $GITHUB_ENV

      - name: Clean Existing SSH Connections
        run: |
          echo "🧹 Cleaning any existing SSH connections..."
          # Kill any hung SSH connections to the instance
          pkill -f "ssh.*$EC2_HOST" || true
          echo "✅ Initial SSH cleanup completed"

      - name: Install System Dependencies
        run: |
          echo "📦 Installing system dependencies..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            # Update system
            sudo yum update -y

            # Install Docker and Docker Compose
            sudo yum install -y docker
            sudo systemctl start docker
            sudo systemctl enable docker
            sudo usermod -a -G docker ec2-user

            # Install Docker Compose
            sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose

            # Install nginx for frontend
            sudo yum install -y nginx

            # Remove any existing PostgreSQL installations to avoid port conflicts
            echo 'Removing native PostgreSQL installations...'
            sudo systemctl stop postgresql || echo 'PostgreSQL not running'
            sudo systemctl disable postgresql || echo 'PostgreSQL not enabled'
            sudo yum remove -y postgresql* || echo 'No PostgreSQL packages to remove'

            echo 'Dependencies installed successfully'
          "



      - name: Copy Repository Files
        run: |
          echo "📤 Copying repository files to EC2..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "mkdir -p /home/<USER>/tuchanga"

          rsync -avz --delete --timeout=300 \
            -e "ssh $SSH_OPTS" \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='dist' \
            --exclude='build' \
            --exclude='.env' \
            --exclude='.env.local' \
            --exclude='.env.development' \
            ./ $EC2_USER@$EC2_HOST:/home/<USER>/tuchanga/

      - name: Deploy Backend
        if: needs.detect-changes.outputs.deploy-backend == 'true'
        run: |
          echo "🔧 Deploying backend with Docker Compose..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            cd /home/<USER>/tuchanga/backend

            # Stop and disable native PostgreSQL service to free port 5432
            echo 'Stopping native PostgreSQL service...'
            sudo systemctl stop postgresql || echo 'PostgreSQL service not running'
            sudo systemctl disable postgresql || echo 'PostgreSQL service not enabled'

            # Stop any existing containers
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml down || echo 'No existing containers to stop'

            # Remove old images to free space
            docker system prune -f || echo 'No images to prune'

            # Build and start services with Docker Compose (production configuration)
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

            # Wait for services to be ready
            echo 'Waiting for services to start...'
            sleep 30

            # Check if services are running
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

            # Check backend health
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs backend --tail=20

            echo 'Backend deployed successfully with Docker Compose'
          "

      - name: Setup Nginx Configuration
        run: |
          echo "🌐 Setting up Nginx configuration..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            # Create nginx configuration for ChanguApp
            sudo tee /etc/nginx/conf.d/changuapp.conf > /dev/null << 'EOF'
            server {
                listen 80;
                server_name _;
                root /var/www/tuchanga/frontend;
                index index.html;

                # Security headers
                add_header X-Frame-Options \"SAMEORIGIN\" always;
                add_header X-XSS-Protection \"1; mode=block\" always;
                add_header X-Content-Type-Options \"nosniff\" always;

                # Frontend
                location / {
                    try_files \$uri \$uri/ /index.html;

                    # Cache static assets
                    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
                        expires 1y;
                        add_header Cache-Control \"public, immutable\";
                    }

                    # No cache for HTML files
                    location ~* \.html\$ {
                        add_header Cache-Control \"no-cache, no-store, must-revalidate\";
                        add_header Pragma \"no-cache\";
                        add_header Expires \"0\";
                    }
                }

                # Backend API proxy
                location /api/ {
                    proxy_pass http://localhost:3000/;
                    proxy_http_version 1.1;
                    proxy_set_header Upgrade \$http_upgrade;
                    proxy_set_header Connection 'upgrade';
                    proxy_set_header Host \$host;
                    proxy_set_header X-Real-IP \$remote_addr;
                    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto \$scheme;
                    proxy_cache_bypass \$http_upgrade;

                    # Timeout settings
                    proxy_connect_timeout 60s;
                    proxy_send_timeout 60s;
                    proxy_read_timeout 60s;
                }

                # Health check endpoint
                location /health {
                    proxy_pass http://localhost:3000/health;
                    access_log off;
                }

                # Direct backend access (for API calls without /api prefix)
                location ~ ^/(auth|jobs|users|chat|notifications|applications)/ {
                    proxy_pass http://localhost:3000;
                    proxy_http_version 1.1;
                    proxy_set_header Upgrade \$http_upgrade;
                    proxy_set_header Connection 'upgrade';
                    proxy_set_header Host \$host;
                    proxy_set_header X-Real-IP \$remote_addr;
                    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto \$scheme;
                    proxy_cache_bypass \$http_upgrade;

                    # Timeout settings
                    proxy_connect_timeout 60s;
                    proxy_send_timeout 60s;
                    proxy_read_timeout 60s;
                }
            }
          EOF

            # Remove default nginx configuration
            sudo rm -f /etc/nginx/sites-enabled/default
            sudo rm -f /etc/nginx/conf.d/default.conf

            # Test nginx configuration
            sudo nginx -t

            echo 'Nginx configuration setup completed'
          "

      - name: Deploy Frontend
        if: needs.detect-changes.outputs.deploy-frontend == 'true'
        run: |
          echo "🎨 Deploying frontend..."
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            cd /home/<USER>/tuchanga/frontend

            # Install and build with timeout handling
            timeout 600 npm ci || { echo 'npm ci timeout'; exit 1; }

            # Set production environment for build
            export NODE_ENV=production

            timeout 600 npm run build || { echo 'npm build timeout'; exit 1; }

            # Deploy to nginx
            sudo mkdir -p /var/www/tuchanga/frontend
            sudo rm -rf /var/www/tuchanga/frontend/*
            sudo cp -r dist/* /var/www/tuchanga/frontend/

            # Set proper permissions
            sudo chown -R nginx:nginx /var/www/tuchanga/frontend

            # Test and restart nginx
            sudo nginx -t && sudo systemctl restart nginx

            echo 'Frontend deployed successfully'
          "

      - name: Verify Deployment
        run: |
          echo "🔍 Verifying deployment..."
          sleep 10

          # Test frontend
          FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")
          echo "🌐 Frontend status: HTTP $FRONTEND_STATUS"

          # Test backend
          BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")
          echo "🔧 Backend status: HTTP $BACKEND_STATUS"

          # Check services
          ssh $SSH_OPTS $EC2_USER@$EC2_HOST "
            echo 'Docker services status:'
            cd /home/<USER>/tuchanga/backend
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps || echo 'Docker Compose not running'

            echo 'Nginx status:'
            sudo systemctl is-active nginx || echo 'Nginx not running'

            echo 'Backend container logs (last 10 lines):'
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs backend --tail=10 || echo 'No backend logs'

            echo 'Database container logs (last 5 lines):'
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs postgres --tail=5 || echo 'No database logs'

            echo 'Backend health check:'
            curl -f http://localhost:3000/health || echo 'Health check failed'
          "

      - name: Cleanup SSH Connections
        if: always()
        run: |
          echo "🧹 Cleaning up SSH connections..."
          # Kill any remaining SSH processes
          pkill -f "ssh.*$EC2_HOST" || true
          echo "✅ SSH cleanup completed"

  notify-completion:
    runs-on: ubuntu-latest
    needs: [detect-changes, deploy-to-ec2]
    if: always() && needs.detect-changes.outputs.should-deploy == 'true'
    steps:
      - name: Deployment Summary
        run: |
          if [[ "${{ needs.deploy-to-ec2.result }}" == "success" ]]; then
            echo "🎉 Deployment completed successfully!"
            echo " Frontend: http://${{ env.EC2_HOST }}/"
            echo "🔧 Backend API: http://${{ env.EC2_HOST }}:3000/"
          else
            echo "❌ Deployment failed! Check logs for details."
          fi
