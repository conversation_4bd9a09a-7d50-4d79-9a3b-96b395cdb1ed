"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkDatabaseStatus = checkDatabaseStatus;
const typeorm_1 = require("typeorm");
async function checkDatabaseStatus() {
    console.log('🔍 Checking database status...');
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || 'postgres123',
        database: process.env.DB_DATABASE || 'job_platform',
        entities: [],
        synchronize: false,
    });
    try {
        await dataSource.initialize();
        console.log('📦 Connected to database');
        const totalUsers = await dataSource.query('SELECT COUNT(*) as count FROM users');
        console.log(`👥 Total users in database: ${totalUsers[0].count}`);
        const totalJobs = await dataSource.query('SELECT COUNT(*) as count FROM jobs WHERE "deletedAt" IS NULL');
        console.log(`💼 Total active jobs in database: ${totalJobs[0].count}`);
        const recentJobs = await dataSource.query(`
      SELECT id, title, location, "createdAt"
      FROM jobs
      WHERE "deletedAt" IS NULL
      ORDER BY "createdAt" DESC
      LIMIT 10
    `);
        if (recentJobs.length > 0) {
            console.log('\n📋 Recent jobs in database:');
            console.log('=====================================');
            recentJobs.forEach((job, index) => {
                console.log(`${index + 1}. ${job.title}`);
                console.log(`   Location: ${job.location}`);
                console.log(`   Created: ${new Date(job.createdAt).toLocaleDateString()}`);
                console.log('');
            });
        }
        else {
            console.log('\n✅ No jobs found in database - all test data has been cleaned!');
        }
        const suspiciousJobs = await dataSource.query(`
      SELECT id, title, description
      FROM jobs
      WHERE "deletedAt" IS NULL
      AND (
        title LIKE '%test%' OR
        title LIKE '%Test%' OR
        description LIKE '%test%' OR
        description LIKE '%Test%' OR
        location LIKE '%Test%'
      )
    `);
        if (suspiciousJobs.length > 0) {
            console.log('\n⚠️ Found potentially remaining test jobs:');
            console.log('==========================================');
            suspiciousJobs.forEach((job, index) => {
                console.log(`${index + 1}. ${job.title}`);
                console.log(`   Description: ${job.description?.substring(0, 100)}...`);
                console.log('');
            });
        }
        else {
            console.log('\n✅ No suspicious test-like jobs found!');
        }
    }
    catch (error) {
        console.error('❌ Error checking database:', error);
    }
    finally {
        await dataSource.destroy();
    }
}
if (require.main === module) {
    checkDatabaseStatus()
        .then(() => {
        console.log('\n🎉 Database status check completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Database status check failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=check-database-status.js.map