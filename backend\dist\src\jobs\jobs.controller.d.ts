import { JobsService } from './jobs.service';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { CompleteJobDto } from './dto/complete-job.dto';
import { Job, JobCategory, JobStatus } from './entities/job.entity';
import { JobTemplate } from './entities/job-template.entity';
import { UsersService } from '../users/users.service';
import { UserResolutionUtil } from '../common/utils/user-resolution.util';
import { ChatService } from '../chat/chat.service';
export declare class JobsController {
    private readonly jobsService;
    private readonly usersService;
    private readonly userResolutionUtil;
    private readonly chatService;
    constructor(jobsService: JobsService, usersService: UsersService, userResolutionUtil: UserResolutionUtil, chatService: ChatService);
    create(createJobDto: CreateJobDto, req: any): Promise<Job>;
    findAll(page?: number, limit?: number, category?: JobCategory, status?: JobStatus, latitude?: number, longitude?: number, radiusKm?: number, excludeUserId?: string, req?: any): Promise<{
        jobs: Job[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    search(searchTerm: string, category?: JobCategory, minBudget?: number, maxBudget?: number, excludeUserId?: string, req?: any): Promise<Job[]>;
    findNearby(latitude: number, longitude: number, radius?: number, category?: JobCategory, excludeUserId?: string): Promise<Job[]>;
    getMyJobs(req: any): Promise<any[]>;
    findOne(id: string): Promise<Job>;
    update(id: string, updateJobDto: UpdateJobDto, req: any): Promise<Job>;
    remove(id: string, req: any): Promise<void>;
    pauseJob(id: string, req: any): Promise<Job>;
    resumeJob(id: string, req: any): Promise<Job>;
    completeJob(id: string, completeJobDto: CompleteJobDto, req: any): Promise<Job>;
    getMyTemplates(req: any): Promise<JobTemplate[]>;
    getTemplate(id: string, req: any): Promise<JobTemplate>;
    deleteTemplate(id: string, req: any): Promise<void>;
    private getUserFromRequest;
}
