import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { Notification } from './entities/notification.entity';
import { UsersService } from '../users/users.service';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly usersService;
    constructor(notificationsService: NotificationsService, usersService: UsersService);
    private getUserIdFromRequest;
    create(createNotificationDto: CreateNotificationDto): Promise<Notification>;
    findAll(req: any, limit?: string, offset?: string): Promise<{
        notifications: Notification[];
        total: number;
        unreadCount: number;
    }>;
    getUnreadCount(req: any): Promise<{
        count: number;
    }>;
    markAsRead(id: string, req: any): Promise<Notification>;
    markAllAsRead(req: any): Promise<{
        message: string;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
