import { User } from '../../users/entities/user.entity';
import { Conversation } from './conversation.entity';
export declare enum MessageType {
    TEXT = "text",
    SYSTEM = "system"
}
export declare class ChatMessage {
    id: string;
    conversation: Conversation;
    conversationId: string;
    sender: User;
    senderId: string;
    content: string;
    type: MessageType;
    isRead: boolean;
    createdAt: Date;
}
