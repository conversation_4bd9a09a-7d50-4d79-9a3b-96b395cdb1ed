import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
export declare class AuthService {
    private readonly jwtService;
    private readonly usersService;
    constructor(jwtService: JwtService, usersService: UsersService);
    validateFirebaseToken(token: string): Promise<any>;
    login(firebaseToken: string): Promise<{
        accessToken: string;
        user: any;
    }>;
    register(firebaseToken: string, userData: any): Promise<{
        accessToken: string;
        user: any;
    }>;
    getCurrentUser(firebaseUid: string): Promise<any>;
    recoverFirebaseBackendSync(firebaseToken: string, userData?: any): Promise<{
        accessToken: string;
        user: any;
    }>;
}
