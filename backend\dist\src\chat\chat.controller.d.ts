import { ChatService } from './chat.service';
import { CreateContactRequestDto } from './dto/create-contact-request.dto';
import { RespondContactRequestDto } from './dto/respond-contact-request.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { UserResolutionUtil } from '../common/utils/user-resolution.util';
export declare class ChatController {
    private readonly chatService;
    private readonly userResolutionUtil;
    constructor(chatService: ChatService, userResolutionUtil: UserResolutionUtil);
    private getUserIdFromRequest;
    createContactRequest(req: any, createContactRequestDto: CreateContactRequestDto): Promise<import("./entities/contact-request.entity").ContactRequest>;
    getContactRequests(req: any): Promise<import("./entities/contact-request.entity").ContactRequest[]>;
    getContactRequestStatus(req: any, jobId: string): Promise<{
        hasContactRequest: boolean;
        status?: import("./entities/contact-request.entity").ContactRequestStatus;
        requestId?: string;
    }>;
    respondToContactRequest(req: any, requestId: string, respondDto: RespondContactRequestDto): Promise<import("./entities/contact-request.entity").ContactRequest>;
    respondToContactRequestPatch(req: any, requestId: string, respondDto: RespondContactRequestDto): Promise<import("./entities/contact-request.entity").ContactRequest>;
    getConversations(req: any): Promise<import("./entities/conversation.entity").Conversation[]>;
    getMessages(req: any, conversationId: string, page?: string, limit?: string): Promise<import("./entities/chat-message.entity").ChatMessage[]>;
    sendMessage(req: any, sendMessageDto: SendMessageDto): Promise<import("./entities/chat-message.entity").ChatMessage>;
    getAcceptedContactRequestsForJob(req: any, jobId: string): Promise<import("./entities/contact-request.entity").ContactRequest[]>;
    getContactRequestCountForJob(req: any, jobId: string): Promise<{
        count: number;
    }>;
    closeConversation(req: any, conversationId: string): Promise<import("./entities/conversation.entity").Conversation>;
    markMessagesAsRead(req: any, conversationId: string): Promise<{
        message: string;
    }>;
}
