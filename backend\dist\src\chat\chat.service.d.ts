import { Repository } from 'typeorm';
import { ContactRequest, ContactRequestStatus } from './entities/contact-request.entity';
import { Conversation } from './entities/conversation.entity';
import { ChatMessage } from './entities/chat-message.entity';
import { CreateContactRequestDto } from './dto/create-contact-request.dto';
import { RespondContactRequestDto } from './dto/respond-contact-request.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { Job } from '../jobs/entities/job.entity';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
export declare class ChatService {
    private contactRequestRepository;
    private conversationRepository;
    private chatMessageRepository;
    private jobRepository;
    private readonly userRepository;
    private readonly notificationsService;
    constructor(contactRequestRepository: Repository<ContactRequest>, conversationRepository: Repository<Conversation>, chatMessageRepository: Repository<ChatMessage>, jobRepository: Repository<Job>, userRepository: Repository<User>, notificationsService: NotificationsService);
    createContactRequest(userId: string, createContactRequestDto: CreateContactRequestDto): Promise<ContactRequest>;
    getContactRequests(userId: string): Promise<ContactRequest[]>;
    getContactRequestStatus(userId: string, jobId: string): Promise<{
        hasContactRequest: boolean;
        status?: ContactRequestStatus;
        requestId?: string;
    }>;
    respondToContactRequest(userId: string, requestId: string, respondDto: RespondContactRequestDto): Promise<ContactRequest>;
    private createConversation;
    getConversations(userId: string): Promise<Conversation[]>;
    getMessages(userId: string, conversationId: string, page?: number, limit?: number): Promise<ChatMessage[]>;
    sendMessage(userId: string, sendMessageDto: SendMessageDto): Promise<ChatMessage>;
    markMessagesAsRead(userId: string, conversationId: string): Promise<void>;
    findUserByFirebaseUid(firebaseUid: string): Promise<User | null>;
    getAcceptedContactRequestsForJob(jobId: string, userId: string): Promise<ContactRequest[]>;
    getContactRequestCountForJob(jobId: string, userId: string): Promise<number>;
    getContactRequestCountsForJobs(jobIds: string[], userId: string): Promise<Map<string, number>>;
    closeConversation(conversationId: string, userId: string): Promise<Conversation>;
}
