"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResolutionUtil = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../../users/users.service");
let UserResolutionUtil = class UserResolutionUtil {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    async getUserFromRequest(req) {
        if (!req.user) {
            throw new Error('No authenticated user found in request');
        }
        if (req.user.sub) {
            try {
                const user = await this.usersService.findOne(req.user.sub);
                return user;
            }
            catch (error) {
            }
        }
        const firebaseUid = req.user.uid || req.user.firebaseUid;
        if (firebaseUid) {
            try {
                const user = await this.usersService.findByFirebaseUid(firebaseUid);
                return user;
            }
            catch (error) {
                throw new Error(`User not found for Firebase UID: ${firebaseUid}`);
            }
        }
        if (req.user.sub) {
            throw new Error(`User not found for ID: ${req.user.sub}`);
        }
        throw new Error('No valid user identifier found in request');
    }
    async getUserIdFromRequest(req) {
        const user = await this.getUserFromRequest(req);
        return user.id;
    }
    async getUserIdFromRequestSafe(req) {
        try {
            return await this.getUserIdFromRequest(req);
        }
        catch (error) {
            return null;
        }
    }
    isAuthenticated(req) {
        return !!(req.user && (req.user.sub || req.user.uid || req.user.firebaseUid));
    }
    async getFallbackUser(fallbackFirebaseUid = 'user1') {
        try {
            return await this.usersService.findByFirebaseUid(fallbackFirebaseUid);
        }
        catch (error) {
            return null;
        }
    }
};
exports.UserResolutionUtil = UserResolutionUtil;
exports.UserResolutionUtil = UserResolutionUtil = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UserResolutionUtil);
//# sourceMappingURL=user-resolution.util.js.map