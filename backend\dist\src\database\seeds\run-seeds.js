"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSeeds = runSeeds;
exports.clearDatabase = clearDatabase;
const typeorm_1 = require("typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../../users/entities/user.entity");
const job_entity_1 = require("../../jobs/entities/job.entity");
const job_template_entity_1 = require("../../jobs/entities/job-template.entity");
const application_entity_1 = require("../../applications/entities/application.entity");
const contact_request_entity_1 = require("../../chat/entities/contact-request.entity");
const conversation_entity_1 = require("../../chat/entities/conversation.entity");
const chat_message_entity_1 = require("../../chat/entities/chat-message.entity");
const development_users_seed_1 = require("./development-users.seed");
async function clearDatabase(dataSource) {
    console.log('🧹 Clearing database...');
    try {
        await dataSource.createQueryBuilder().delete().from(chat_message_entity_1.ChatMessage).execute();
        await dataSource.createQueryBuilder().delete().from(conversation_entity_1.Conversation).execute();
        await dataSource.createQueryBuilder().delete().from(contact_request_entity_1.ContactRequest).execute();
        await dataSource.createQueryBuilder().delete().from(application_entity_1.Application).execute();
        await dataSource.createQueryBuilder().delete().from(job_template_entity_1.JobTemplate).execute();
        await dataSource.createQueryBuilder().delete().from(job_entity_1.Job).execute();
        await dataSource.createQueryBuilder().delete().from(user_entity_1.User).execute();
        console.log('✅ Database cleared successfully');
    }
    catch (error) {
        console.error('❌ Error clearing database:', error);
        throw error;
    }
}
async function runSeeds() {
    const configService = new config_1.ConfigService();
    if (configService.get('NODE_ENV') === 'production') {
        console.log('🚫 Skipping seeds in production environment');
        return;
    }
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'postgres123'),
        database: configService.get('DB_DATABASE', 'job_platform'),
        entities: [user_entity_1.User, job_entity_1.Job, job_template_entity_1.JobTemplate, application_entity_1.Application, contact_request_entity_1.ContactRequest, conversation_entity_1.Conversation, chat_message_entity_1.ChatMessage],
        synchronize: false,
    });
    try {
        await dataSource.initialize();
        console.log('📦 Database connection established for seeding');
        await clearDatabase(dataSource);
        await (0, development_users_seed_1.seedDevelopmentUsers)(dataSource);
        console.log('✅ All seeds completed successfully');
    }
    catch (error) {
        console.error('❌ Error running seeds:', error);
    }
    finally {
        await dataSource.destroy();
    }
}
if (require.main === module) {
    runSeeds();
}
//# sourceMappingURL=run-seeds.js.map