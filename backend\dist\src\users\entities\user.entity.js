"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = exports.UserRole = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const job_entity_1 = require("../../jobs/entities/job.entity");
const application_entity_1 = require("../../applications/entities/application.entity");
const job_template_entity_1 = require("../../jobs/entities/job-template.entity");
var UserRole;
(function (UserRole) {
    UserRole["EMPLOYER"] = "employer";
    UserRole["EMPLOYEE"] = "employee";
    UserRole["BOTH"] = "both";
})(UserRole || (exports.UserRole = UserRole = {}));
let User = class User {
    id;
    email;
    firebaseUid;
    firstName;
    lastName;
    phone;
    role;
    profilePicture;
    bio;
    skills;
    location;
    latitude;
    longitude;
    hourlyRate;
    isAvailable;
    isVip;
    completedJobs;
    postedJobsCompleted;
    createdAt;
    updatedAt;
    postedJobs;
    applications;
    jobTemplates;
};
exports.User = User;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User unique identifier' }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User email address' }),
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Firebase UID for authentication' }),
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "firebaseUid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User first name' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User last name' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User phone number', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User role in the platform', enum: UserRole }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: UserRole.BOTH,
    }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User profile picture URL', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "profilePicture", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User bio/description', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "bio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User skills (comma-separated)', required: false }),
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], User.prototype, "skills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User location (city, state)', required: false }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User latitude for location-based search', required: false }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 8, nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User longitude for location-based search', required: false }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 11, scale: 8, nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User hourly rate for services', required: false }),
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "hourlyRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether user is available for work', default: true }),
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "isAvailable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether user has VIP status for urgent jobs', default: false }),
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of completed jobs', default: 0 }),
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "completedJobs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of posted jobs that were completed', default: 0 }),
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "postedJobsCompleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User account creation date' }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User account last update date' }),
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Jobs posted by user', type: () => [job_entity_1.Job] }),
    (0, typeorm_1.OneToMany)(() => job_entity_1.Job, (job) => job.employer),
    __metadata("design:type", Array)
], User.prototype, "postedJobs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job applications by user', type: () => [application_entity_1.Application] }),
    (0, typeorm_1.OneToMany)(() => application_entity_1.Application, (application) => application.applicant),
    __metadata("design:type", Array)
], User.prototype, "applications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job templates created by user', type: () => [job_template_entity_1.JobTemplate] }),
    (0, typeorm_1.OneToMany)(() => job_template_entity_1.JobTemplate, (template) => template.user),
    __metadata("design:type", Array)
], User.prototype, "jobTemplates", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);
//# sourceMappingURL=user.entity.js.map