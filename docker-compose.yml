services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: changuapp-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: job_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-ChanguApp2024!SecureDB}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backend/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - changuapp-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d job_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # NestJS Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    container_name: changuapp-backend-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: ${DB_PASSWORD:-ChanguApp2024!SecureDB}
      DB_DATABASE: job_platform
      JWT_SECRET: ${JWT_SECRET:-ChanguApp2024!SuperSecretJWTKey!ChangeInProduction}
      JWT_EXPIRES_IN: "24h"
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID:-tu-changa-583b3}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY:-}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL:-}
      PORT: 3000
      CORS_ORIGIN: "*"
      API_PREFIX: "api/v1"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - changuapp-network-prod
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: changuapp-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - changuapp-network-prod
    volumes:
      - redis_data_prod:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-ChanguApp2024!Redis}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local

networks:
  changuapp-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
