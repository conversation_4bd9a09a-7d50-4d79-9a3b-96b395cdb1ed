"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Conversation = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const job_entity_1 = require("../../jobs/entities/job.entity");
const chat_message_entity_1 = require("./chat-message.entity");
let Conversation = class Conversation {
    id;
    user1;
    user1Id;
    user2;
    user2Id;
    job;
    jobId;
    messages;
    lastMessageAt;
    lastMessagePreview;
    unreadCountUser1;
    unreadCountUser2;
    isClosed;
    closedByUserId;
    closedAt;
    createdAt;
    updatedAt;
};
exports.Conversation = Conversation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Conversation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'user1_id' }),
    __metadata("design:type", user_entity_1.User)
], Conversation.prototype, "user1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user1_id' }),
    __metadata("design:type", String)
], Conversation.prototype, "user1Id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'user2_id' }),
    __metadata("design:type", user_entity_1.User)
], Conversation.prototype, "user2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user2_id' }),
    __metadata("design:type", String)
], Conversation.prototype, "user2Id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => job_entity_1.Job, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'job_id' }),
    __metadata("design:type", job_entity_1.Job)
], Conversation.prototype, "job", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'job_id' }),
    __metadata("design:type", String)
], Conversation.prototype, "jobId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => chat_message_entity_1.ChatMessage, message => message.conversation),
    __metadata("design:type", Array)
], Conversation.prototype, "messages", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_message_at', nullable: true }),
    __metadata("design:type", Date)
], Conversation.prototype, "lastMessageAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_message_preview', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Conversation.prototype, "lastMessagePreview", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unread_count_user1', type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Conversation.prototype, "unreadCountUser1", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'unread_count_user2', type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Conversation.prototype, "unreadCountUser2", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_closed', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Conversation.prototype, "isClosed", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'closed_by_user_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Conversation.prototype, "closedByUserId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'closed_at', nullable: true }),
    __metadata("design:type", Date)
], Conversation.prototype, "closedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], Conversation.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Conversation.prototype, "updatedAt", void 0);
exports.Conversation = Conversation = __decorate([
    (0, typeorm_1.Entity)('conversations')
], Conversation);
//# sourceMappingURL=conversation.entity.js.map