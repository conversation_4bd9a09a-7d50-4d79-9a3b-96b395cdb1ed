"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const chat_service_1 = require("./chat.service");
const create_contact_request_dto_1 = require("./dto/create-contact-request.dto");
const respond_contact_request_dto_1 = require("./dto/respond-contact-request.dto");
const send_message_dto_1 = require("./dto/send-message.dto");
const optional_auth_guard_1 = require("../auth/guards/optional-auth.guard");
const user_resolution_util_1 = require("../common/utils/user-resolution.util");
let ChatController = class ChatController {
    chatService;
    userResolutionUtil;
    constructor(chatService, userResolutionUtil) {
        this.chatService = chatService;
        this.userResolutionUtil = userResolutionUtil;
    }
    async getUserIdFromRequest(req) {
        try {
            return await this.userResolutionUtil.getUserIdFromRequest(req);
        }
        catch (error) {
            const fallbackUser = await this.userResolutionUtil.getFallbackUser('user1');
            if (fallbackUser) {
                return fallbackUser.id;
            }
            throw new Error('No authenticated user and no fallback user found');
        }
    }
    async createContactRequest(req, createContactRequestDto) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.createContactRequest(userId, createContactRequestDto);
    }
    async getContactRequests(req) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.getContactRequests(userId);
    }
    async getContactRequestStatus(req, jobId) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.getContactRequestStatus(userId, jobId);
    }
    async respondToContactRequest(req, requestId, respondDto) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.respondToContactRequest(userId, requestId, respondDto);
    }
    async respondToContactRequestPatch(req, requestId, respondDto) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.respondToContactRequest(userId, requestId, respondDto);
    }
    async getConversations(req) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.getConversations(userId);
    }
    async getMessages(req, conversationId, page = '1', limit = '50') {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.getMessages(userId, conversationId, parseInt(page), parseInt(limit));
    }
    async sendMessage(req, sendMessageDto) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.sendMessage(userId, sendMessageDto);
    }
    async getAcceptedContactRequestsForJob(req, jobId) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.getAcceptedContactRequestsForJob(jobId, userId);
    }
    async getContactRequestCountForJob(req, jobId) {
        const userId = await this.getUserIdFromRequest(req);
        const count = await this.chatService.getContactRequestCountForJob(jobId, userId);
        return { count };
    }
    async closeConversation(req, conversationId) {
        const userId = await this.getUserIdFromRequest(req);
        return await this.chatService.closeConversation(conversationId, userId);
    }
    async markMessagesAsRead(req, conversationId) {
        const userId = await this.getUserIdFromRequest(req);
        await this.chatService.markMessagesAsRead(userId, conversationId);
        return { message: 'Messages marked as read' };
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Post)('contact-requests'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a contact request for a job' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Contact request sent successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - duplicate request or self-contact' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_contact_request_dto_1.CreateContactRequestDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "createContactRequest", null);
__decorate([
    (0, common_1.Get)('contact-requests'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pending contact requests for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Contact requests retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getContactRequests", null);
__decorate([
    (0, common_1.Get)('contact-requests/job/:jobId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Check if current user has sent a contact request for a specific job' }),
    (0, swagger_1.ApiParam)({ name: 'jobId', description: 'Job ID to check contact request status for' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Contact request status retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getContactRequestStatus", null);
__decorate([
    (0, common_1.Put)('contact-requests/:id/respond'),
    (0, swagger_1.ApiOperation)({ summary: 'Respond to a contact request (accept/reject)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact request ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Contact request response processed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not your contact request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Contact request not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, respond_contact_request_dto_1.RespondContactRequestDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "respondToContactRequest", null);
__decorate([
    (0, common_1.Patch)('contact-requests/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Respond to a contact request (accept/reject) - PATCH alias' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Contact request ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Contact request response processed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not your contact request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Contact request not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, respond_contact_request_dto_1.RespondContactRequestDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "respondToContactRequestPatch", null);
__decorate([
    (0, common_1.Get)('conversations'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all conversations for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversations retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getConversations", null);
__decorate([
    (0, common_1.Get)('conversations/:id/messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Get messages for a conversation' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Conversation ID' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Messages per page (default: 50)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Messages retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not part of conversation' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getMessages", null);
__decorate([
    (0, common_1.Post)('messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a message in a conversation' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Message sent successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not part of conversation' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, send_message_dto_1.SendMessageDto]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Get)('jobs/:jobId/accepted-contacts'),
    (0, swagger_1.ApiOperation)({ summary: 'Get accepted contact requests for a job' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Accepted contact requests retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getAcceptedContactRequestsForJob", null);
__decorate([
    (0, common_1.Get)('jobs/:jobId/contact-count'),
    (0, swagger_1.ApiOperation)({ summary: 'Get total contact request count for a job' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Contact request count retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getContactRequestCountForJob", null);
__decorate([
    (0, common_1.Patch)('conversations/:id/close'),
    (0, swagger_1.ApiOperation)({ summary: 'Close a conversation (job poster only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Conversation closed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job poster' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "closeConversation", null);
__decorate([
    (0, common_1.Put)('conversations/:id/mark-read'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark all messages in a conversation as read' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Conversation ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Messages marked as read successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not part of conversation' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conversation not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "markMessagesAsRead", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('chat'),
    (0, common_1.Controller)('chat'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [chat_service_1.ChatService,
        user_resolution_util_1.UserResolutionUtil])
], ChatController);
//# sourceMappingURL=chat.controller.js.map