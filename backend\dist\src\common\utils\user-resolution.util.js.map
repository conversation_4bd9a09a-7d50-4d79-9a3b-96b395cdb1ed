{"version": 3, "file": "user-resolution.util.js", "sourceRoot": "", "sources": ["../../../../src/common/utils/user-resolution.util.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6DAAyD;AAelD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IACA;IAA7B,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;IAAG,CAAC;IAQ3D,KAAK,CAAC,kBAAkB,CAAC,GAAyB;QAChD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,IAAI,CAAC;gBAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;YAGjB,CAAC;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;QACzD,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACpE,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,oCAAoC,WAAW,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;IAC/D,CAAC;IAQD,KAAK,CAAC,oBAAoB,CAAC,GAAyB;QAClD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAOD,KAAK,CAAC,wBAAwB,CAAC,GAAyB;QACtD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAOD,eAAe,CAAC,GAAyB;QACvC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAChF,CAAC;IAOD,KAAK,CAAC,eAAe,CAAC,sBAA8B,OAAO;QACzD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA1FY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAEgC,4BAAY;GAD5C,kBAAkB,CA0F9B"}