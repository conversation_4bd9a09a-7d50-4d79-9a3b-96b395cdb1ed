import { AuthService } from './auth.service';
import { LoginDto, RegisterDto } from './dto/login.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<{
        accessToken: string;
        user: any;
    }>;
    register(registerDto: RegisterDto): Promise<{
        accessToken: string;
        user: any;
    }>;
    syncRecovery(body: {
        firebaseToken: string;
        userData?: any;
    }): Promise<{
        accessToken: string;
        user: any;
    }>;
    getCurrentUser(req: any): Promise<any>;
}
