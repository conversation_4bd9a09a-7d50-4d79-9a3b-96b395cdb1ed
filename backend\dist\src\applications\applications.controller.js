"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const applications_service_1 = require("./applications.service");
const create_application_dto_1 = require("./dto/create-application.dto");
const update_application_dto_1 = require("./dto/update-application.dto");
const application_entity_1 = require("./entities/application.entity");
const firebase_auth_guard_1 = require("../auth/guards/firebase-auth.guard");
const users_service_1 = require("../users/users.service");
let ApplicationsController = class ApplicationsController {
    applicationsService;
    usersService;
    constructor(applicationsService, usersService) {
        this.applicationsService = applicationsService;
        this.usersService = usersService;
    }
    async create(createApplicationDto, req) {
        const user = await this.getUserFromRequest(req);
        return this.applicationsService.create(createApplicationDto, user.id);
    }
    findAll() {
        return this.applicationsService.findAll();
    }
    async getMyApplications(req) {
        const user = await this.getUserFromRequest(req);
        return this.applicationsService.findByApplicant(user.id);
    }
    getJobApplications(jobId) {
        return this.applicationsService.findByJob(jobId);
    }
    findOne(id) {
        return this.applicationsService.findOne(id);
    }
    async update(id, updateApplicationDto, req) {
        const user = await this.getUserFromRequest(req);
        return this.applicationsService.update(id, updateApplicationDto, user.id);
    }
    async withdraw(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.applicationsService.withdraw(id, user.id);
    }
    async remove(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.applicationsService.remove(id, user.id);
    }
    async getUserFromRequest(req) {
        try {
            const firebaseUid = req.user.uid;
            console.log('🔍 Looking up user with Firebase UID:', firebaseUid);
            const user = await this.usersService.findByFirebaseUid(firebaseUid);
            console.log('✅ Found database user:', user.id, user.email);
            return user;
        }
        catch (error) {
            console.error('❌ Error looking up user:', error.message);
            throw new Error(`User not found for Firebase UID: ${req.user.uid}`);
        }
    }
};
exports.ApplicationsController = ApplicationsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Apply for a job' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Application created successfully', type: application_entity_1.Application }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Already applied for this job' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_application_dto_1.CreateApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all applications (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Applications retrieved successfully', type: [application_entity_1.Application] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('my-applications'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user applications' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User applications retrieved successfully', type: [application_entity_1.Application] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "getMyApplications", null);
__decorate([
    (0, common_1.Get)('job/:jobId'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get applications for a specific job' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job applications retrieved successfully', type: [application_entity_1.Application] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('jobId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "getJobApplications", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get application by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application retrieved successfully', type: application_entity_1.Application }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update application status (employer only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application updated successfully', type: application_entity_1.Application }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job employer' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_application_dto_1.UpdateApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/withdraw'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Withdraw application (applicant only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application withdrawn successfully', type: application_entity_1.Application }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not application owner' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot withdraw non-pending application' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "withdraw", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete application (applicant only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not application owner' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ApplicationsController.prototype, "remove", null);
exports.ApplicationsController = ApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('applications'),
    (0, common_1.Controller)('applications'),
    __metadata("design:paramtypes", [applications_service_1.ApplicationsService,
        users_service_1.UsersService])
], ApplicationsController);
//# sourceMappingURL=applications.controller.js.map