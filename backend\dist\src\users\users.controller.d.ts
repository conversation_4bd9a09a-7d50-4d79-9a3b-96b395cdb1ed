import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<User>;
    findAll(): Promise<User[]>;
    findNearby(latitude: number, longitude: number, radius?: number): Promise<User[]>;
    findOne(id: string): Promise<User>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<User>;
    remove(id: string): Promise<void>;
    toggleVipStatus(id: string): Promise<User>;
    getCurrentUserProfile(req: any): Promise<User>;
    updateCurrentUserProfile(req: any, updateUserDto: UpdateUserDto): Promise<User>;
    uploadProfilePhoto(req: any, file: Express.Multer.File): Promise<User>;
}
