"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const job_entity_1 = require("./entities/job.entity");
const job_template_entity_1 = require("./entities/job-template.entity");
const user_entity_1 = require("../users/entities/user.entity");
const complete_job_dto_1 = require("./dto/complete-job.dto");
const chat_service_1 = require("../chat/chat.service");
let JobsService = class JobsService {
    jobRepository;
    jobTemplateRepository;
    userRepository;
    chatService;
    constructor(jobRepository, jobTemplateRepository, userRepository, chatService) {
        this.jobRepository = jobRepository;
        this.jobTemplateRepository = jobTemplateRepository;
        this.userRepository = userRepository;
        this.chatService = chatService;
    }
    async create(createJobDto, employerId) {
        if (createJobDto.isUrgent) {
            const user = await this.userRepository.findOne({ where: { id: employerId } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (!user.isVip) {
                throw new common_1.ForbiddenException('Only VIP users can create urgent jobs');
            }
        }
        const job = this.jobRepository.create({
            ...createJobDto,
            employerId,
            status: job_entity_1.JobStatus.OPEN,
            startDate: createJobDto.startDate ? new Date(createJobDto.startDate) : undefined,
            deadline: createJobDto.deadline ? new Date(createJobDto.deadline) : undefined,
        });
        const savedJob = await this.jobRepository.save(job);
        await this.createTemplateFromJob(savedJob, employerId);
        return savedJob;
    }
    async findAll(page = 1, limit = 10, category, status, latitude, longitude, radiusKm, excludeUserId) {
        const queryBuilder = this.jobRepository.createQueryBuilder('job')
            .leftJoinAndSelect('job.employer', 'employer')
            .where('job.deletedAt IS NULL');
        if (category) {
            queryBuilder.andWhere('job.category = :category', { category });
        }
        if (status) {
            queryBuilder.andWhere('job.status = :status', { status });
        }
        else {
            queryBuilder.andWhere('job.status = :status', { status: job_entity_1.JobStatus.OPEN });
        }
        if (excludeUserId) {
            queryBuilder.andWhere('job.employerId != :excludeUserId', { excludeUserId });
        }
        if (latitude && longitude && radiusKm) {
            const distanceFormula = `(6371 * acos(cos(radians(:latitude)) * cos(radians(job.latitude)) * cos(radians(job.longitude) - radians(:longitude)) + sin(radians(:latitude)) * sin(radians(job.latitude))))`;
            queryBuilder.andWhere(`${distanceFormula} <= :radiusKm`, { latitude, longitude, radiusKm });
            queryBuilder.addSelect(distanceFormula, 'distance');
            queryBuilder.orderBy('distance', 'ASC');
            queryBuilder.addOrderBy('job.createdAt', 'DESC');
        }
        else {
            queryBuilder.orderBy('job.createdAt', 'DESC');
        }
        const total = await queryBuilder.getCount();
        const jobs = await queryBuilder
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();
        return {
            jobs,
            total,
            page,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findOne(id) {
        const job = await this.jobRepository.findOne({
            where: { id },
            relations: ['employer', 'applications'],
            withDeleted: false,
        });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        return job;
    }
    async findByEmployer(employerId) {
        return await this.jobRepository.find({
            where: { employerId },
            relations: ['employer', 'applications'],
            order: { createdAt: 'DESC' },
            withDeleted: false,
        });
    }
    async update(id, updateJobDto, userId) {
        const job = await this.jobRepository.findOne({ where: { id } });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only update your own jobs');
        }
        const updatedJob = {
            ...job,
            ...updateJobDto,
            startDate: updateJobDto.startDate ? new Date(updateJobDto.startDate) : job.startDate,
            deadline: updateJobDto.deadline ? new Date(updateJobDto.deadline) : job.deadline,
        };
        return await this.jobRepository.save(updatedJob);
    }
    async remove(id, userId) {
        const job = await this.jobRepository.findOne({ where: { id } });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own jobs');
        }
        await this.jobRepository.softDelete(id);
    }
    async pauseJob(id, userId) {
        const job = await this.jobRepository.findOne({ where: { id } });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only pause your own jobs');
        }
        if (job.status !== job_entity_1.JobStatus.OPEN) {
            throw new common_1.BadRequestException('Only open jobs can be paused');
        }
        job.status = job_entity_1.JobStatus.PAUSED;
        return await this.jobRepository.save(job);
    }
    async resumeJob(id, userId) {
        const job = await this.jobRepository.findOne({ where: { id } });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only resume your own jobs');
        }
        if (job.status !== job_entity_1.JobStatus.PAUSED) {
            throw new common_1.BadRequestException('Only paused jobs can be resumed');
        }
        job.status = job_entity_1.JobStatus.OPEN;
        return await this.jobRepository.save(job);
    }
    async completeJob(id, userId, completeJobDto) {
        const job = await this.jobRepository.findOne({
            where: { id },
            relations: ['employer']
        });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only complete your own jobs');
        }
        if (job.status === job_entity_1.JobStatus.COMPLETED) {
            throw new common_1.BadRequestException('Job is already completed');
        }
        if (job.status === job_entity_1.JobStatus.CANCELLED) {
            throw new common_1.BadRequestException('Cancelled jobs cannot be completed');
        }
        const contactRequestCount = await this.chatService.getContactRequestCountForJob(id, userId);
        if (contactRequestCount === 0) {
            throw new common_1.BadRequestException('Cannot complete job without any contact requests. Jobs can only be completed when there is at least one interested worker.');
        }
        const completionType = completeJobDto?.completionType || complete_job_dto_1.JobCompletionType.COMPLETED;
        if (completionType === complete_job_dto_1.JobCompletionType.COMPLETED) {
            job.status = job_entity_1.JobStatus.COMPLETED;
            job.completedAt = new Date();
            if (completeJobDto?.completedById) {
                const worker = await this.userRepository.findOne({
                    where: { id: completeJobDto.completedById }
                });
                if (!worker) {
                    throw new common_1.NotFoundException('Worker not found');
                }
                job.completedById = completeJobDto.completedById;
                worker.completedJobs = (worker.completedJobs || 0) + 1;
                await this.userRepository.save(worker);
            }
            const employer = job.employer;
            employer.postedJobsCompleted = (employer.postedJobsCompleted || 0) + 1;
            await this.userRepository.save(employer);
        }
        else {
            job.status = job_entity_1.JobStatus.CANCELLED;
        }
        return await this.jobRepository.save(job);
    }
    async findNearbyJobs(latitude, longitude, radiusKm = 50, category, excludeUserId) {
        const queryBuilder = this.jobRepository.createQueryBuilder('job')
            .leftJoinAndSelect('job.employer', 'employer')
            .where('job.status = :status', { status: job_entity_1.JobStatus.OPEN })
            .andWhere('job.deletedAt IS NULL');
        if (excludeUserId) {
            queryBuilder.andWhere('job.employerId != :excludeUserId', { excludeUserId });
        }
        if (category) {
            queryBuilder.andWhere('job.category = :category', { category });
        }
        const distanceFormula = `(6371 * acos(cos(radians(:latitude)) * cos(radians(job.latitude)) * cos(radians(job.longitude) - radians(:longitude)) + sin(radians(:latitude)) * sin(radians(job.latitude))))`;
        queryBuilder.andWhere(`${distanceFormula} <= :radiusKm`, { latitude, longitude, radiusKm });
        queryBuilder.addSelect(distanceFormula, 'distance');
        queryBuilder.orderBy('distance', 'ASC');
        queryBuilder.addOrderBy('job.createdAt', 'DESC');
        return await queryBuilder.getMany();
    }
    async searchJobs(searchTerm, category, minBudget, maxBudget, excludeUserId) {
        const queryBuilder = this.jobRepository.createQueryBuilder('job')
            .leftJoinAndSelect('job.employer', 'employer')
            .where('job.status = :status', { status: job_entity_1.JobStatus.OPEN })
            .andWhere('job.deletedAt IS NULL');
        if (excludeUserId) {
            queryBuilder.andWhere('job.employerId != :excludeUserId', { excludeUserId });
        }
        if (searchTerm) {
            queryBuilder.andWhere('(LOWER(job.title) LIKE LOWER(:searchTerm) OR LOWER(job.description) LIKE LOWER(:searchTerm) OR LOWER(job.requiredSkills) LIKE LOWER(:searchTerm))', { searchTerm: `%${searchTerm}%` });
        }
        if (category) {
            queryBuilder.andWhere('job.category = :category', { category });
        }
        if (minBudget) {
            queryBuilder.andWhere('job.budget >= :minBudget', { minBudget });
        }
        if (maxBudget) {
            queryBuilder.andWhere('job.budget <= :maxBudget', { maxBudget });
        }
        return await queryBuilder.orderBy('job.createdAt', 'DESC').getMany();
    }
    async createTemplateFromJob(job, userId) {
        const template = this.jobTemplateRepository.create({
            title: job.title,
            description: job.description,
            category: job.category,
            budget: job.budget,
            location: job.location,
            latitude: job.latitude,
            longitude: job.longitude,
            requiredSkills: job.requiredSkills,
            estimatedHours: job.estimatedHours,
            isUrgent: job.isUrgent,
            images: job.images,
            icon: job.icon,
            paymentMethods: job.paymentMethods,
            userId,
        });
        await this.jobTemplateRepository.save(template);
        const userTemplates = await this.jobTemplateRepository.find({
            where: { userId },
            order: { createdAt: 'DESC' },
        });
        if (userTemplates.length > 5) {
            const templatesToDelete = userTemplates.slice(5);
            await this.jobTemplateRepository.remove(templatesToDelete);
        }
    }
    async getUserTemplates(userId) {
        return await this.jobTemplateRepository.find({
            where: { userId },
            order: { createdAt: 'DESC' },
            take: 5,
        });
    }
    async getTemplate(templateId, userId) {
        const template = await this.jobTemplateRepository.findOne({
            where: { id: templateId, userId },
        });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return template;
    }
    async deleteTemplate(templateId, userId) {
        const template = await this.getTemplate(templateId, userId);
        await this.jobTemplateRepository.remove(template);
    }
};
exports.JobsService = JobsService;
exports.JobsService = JobsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(job_entity_1.Job)),
    __param(1, (0, typeorm_1.InjectRepository)(job_template_entity_1.JobTemplate)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => chat_service_1.ChatService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        chat_service_1.ChatService])
], JobsService);
//# sourceMappingURL=jobs.service.js.map