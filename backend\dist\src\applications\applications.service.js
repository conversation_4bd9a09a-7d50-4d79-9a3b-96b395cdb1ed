"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const application_entity_1 = require("./entities/application.entity");
let ApplicationsService = class ApplicationsService {
    applications = [];
    constructor() { }
    async create(createApplicationDto, applicantId) {
        const existingApplication = this.applications.find(app => app.jobId === createApplicationDto.jobId && app.applicantId === applicantId);
        if (existingApplication) {
            throw new common_1.ConflictException('You have already applied for this job');
        }
        const application = {
            id: Math.random().toString(36).substr(2, 9),
            ...createApplicationDto,
            applicantId,
            status: application_entity_1.ApplicationStatus.PENDING,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.applications.push(application);
        return application;
    }
    async findAll() {
        return this.applications;
    }
    async findOne(id) {
        const application = this.applications.find(app => app.id === id);
        if (!application) {
            throw new common_1.NotFoundException('Application not found');
        }
        return application;
    }
    async findByJob(jobId) {
        return this.applications
            .filter(app => app.jobId === jobId)
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    async findByApplicant(applicantId) {
        return this.applications
            .filter(app => app.applicantId === applicantId)
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    }
    async update(id, updateApplicationDto, userId) {
        const appIndex = this.applications.findIndex(app => app.id === id);
        if (appIndex === -1) {
            throw new common_1.NotFoundException('Application not found');
        }
        this.applications[appIndex] = {
            ...this.applications[appIndex],
            ...updateApplicationDto,
            updatedAt: new Date(),
        };
        return this.applications[appIndex];
    }
    async withdraw(id, userId) {
        const appIndex = this.applications.findIndex(app => app.id === id);
        if (appIndex === -1) {
            throw new common_1.NotFoundException('Application not found');
        }
        const application = this.applications[appIndex];
        if (application.applicantId !== userId) {
            throw new common_1.ForbiddenException('You can only withdraw your own applications');
        }
        if (application.status !== application_entity_1.ApplicationStatus.PENDING) {
            throw new common_1.ConflictException('You can only withdraw pending applications');
        }
        this.applications[appIndex] = {
            ...application,
            status: application_entity_1.ApplicationStatus.WITHDRAWN,
            updatedAt: new Date(),
        };
        return this.applications[appIndex];
    }
    async remove(id, userId) {
        const appIndex = this.applications.findIndex(app => app.id === id);
        if (appIndex === -1) {
            throw new common_1.NotFoundException('Application not found');
        }
        const application = this.applications[appIndex];
        if (application.applicantId !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own applications');
        }
        this.applications.splice(appIndex, 1);
    }
};
exports.ApplicationsService = ApplicationsService;
exports.ApplicationsService = ApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ApplicationsService);
//# sourceMappingURL=applications.service.js.map