"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateJobDto = void 0;
const mapped_types_1 = require("@nestjs/mapped-types");
const create_job_dto_1 = require("./create-job.dto");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const job_entity_1 = require("../entities/job.entity");
class UpdateJobDto extends (0, mapped_types_1.PartialType)(create_job_dto_1.CreateJobDto) {
    status;
    budget;
}
exports.UpdateJobDto = UpdateJobDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job status', enum: job_entity_1.JobStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(job_entity_1.JobStatus),
    __metadata("design:type", String)
], UpdateJobDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job budget/payment amount', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateJobDto.prototype, "budget", void 0);
//# sourceMappingURL=update-job.dto.js.map