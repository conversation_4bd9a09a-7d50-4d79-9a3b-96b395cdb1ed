import { User } from '../../users/entities/user.entity';
export declare enum NotificationType {
    CONTACT_REQUEST = "contact_request",
    MESSAGE = "message",
    JOB_APPLICATION = "job_application",
    JOB_UPDATE = "job_update"
}
export declare class Notification {
    id: string;
    type: NotificationType;
    title: string;
    message: string;
    isRead: boolean;
    relatedEntityId?: string;
    relatedEntityType?: string;
    metadata?: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    userId: string;
}
