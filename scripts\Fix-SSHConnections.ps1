Run echo "📤 Copying repository files to EC2..."
📤 Copying repository files to EC2...
Can't open user config file ~/.ssh/config: No such file or directory
rsync: connection unexpectedly closed (0 bytes received so far) [sender]
rsync error: unexplained error (code 255) at io.c(232) [sender=3.2.7]
Error: Process completed with exit code 255.# SSH Connection Diagnostic and Fix Script for ChanguApp EC2 Instance (PowerShell)
# This script helps diagnose and fix SSH connection issues on Windows

param(
    [switch]$KillHungProcesses = $false
)

# Configuration
$EC2_HOST = "ec2-54-233-23-129.sa-east-1.compute.amazonaws.com"
$EC2_USER = "ec2-user"
$SSH_KEY = ".\backend\keys\tuchanga.pem"

Write-Host "🔍 ChanguApp SSH Connection Diagnostic Tool" -ForegroundColor Blue
Write-Host "=============================================="

function Write-Status {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if SSH key exists
if (-not (Test-Path $SSH_KEY)) {
    Write-Error "SSH key not found at $SSH_KEY"
    exit 1
}

Write-Info "Using SSH key: $SSH_KEY"
Write-Info "Target host: $EC2_HOST"
Write-Info "User: $EC2_USER"

# Test basic connectivity
Write-Info "Testing basic connectivity..."
try {
    $ping = Test-Connection -ComputerName $EC2_HOST -Count 1 -Quiet
    if ($ping) {
        Write-Status "Host is reachable via ping"
    } else {
        Write-Error "Host is not reachable via ping"
        exit 1
    }
} catch {
    Write-Error "Failed to ping host: $($_.Exception.Message)"
    exit 1
}

# Test SSH connection
Write-Info "Testing SSH connection..."
$sshArgs = @(
    "-i", $SSH_KEY,
    "-o", "ConnectTimeout=10",
    "-o", "StrictHostKeyChecking=no",
    "-o", "BatchMode=yes",
    "$EC2_USER@$EC2_HOST",
    "echo 'SSH connection successful'"
)

try {
    $result = & ssh @sshArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SSH connection is working"
    } else {
        Write-Error "SSH connection failed"
        
        Write-Info "Attempting to get verbose SSH output..."
        $verboseArgs = @(
            "-v",
            "-i", $SSH_KEY,
            "-o", "ConnectTimeout=10",
            "-o", "StrictHostKeyChecking=no",
            "$EC2_USER@$EC2_HOST",
            "echo 'test'"
        )
        
        & ssh @verboseArgs
        
        Write-Info "Common fixes to try:"
        Write-Host "1. Check if the EC2 instance is running"
        Write-Host "2. Verify security group allows SSH (port 22)"
        Write-Host "3. Check if the SSH key is correct"
        Write-Host "4. Try restarting the EC2 instance"
        
        exit 1
    }
} catch {
    Write-Error "SSH test failed: $($_.Exception.Message)"
    exit 1
}

# Check for hung SSH processes
Write-Info "Checking for hung SSH processes..."
try {
    $sshProcesses = Get-Process | Where-Object { $_.ProcessName -eq "ssh" -and $_.CommandLine -like "*$EC2_HOST*" }
    
    if ($sshProcesses.Count -gt 0) {
        Write-Warning "Found $($sshProcesses.Count) potentially hung SSH processes"
        $sshProcesses | Format-Table Id, ProcessName, StartTime, CPU
        
        if ($KillHungProcesses) {
            Write-Info "Killing hung SSH processes..."
            $sshProcesses | Stop-Process -Force
            Write-Status "Hung SSH processes killed"
        } else {
            Write-Info "Use -KillHungProcesses parameter to kill these processes"
        }
    } else {
        Write-Status "No hung SSH processes found"
    }
} catch {
    Write-Warning "Could not check for hung processes: $($_.Exception.Message)"
}

# Check SSH daemon status on remote server
Write-Info "Checking SSH daemon status on remote server..."
try {
    $sshStatusArgs = @(
        "-i", $SSH_KEY,
        "-o", "ConnectTimeout=10",
        "-o", "StrictHostKeyChecking=no",
        "$EC2_USER@$EC2_HOST",
        "sudo systemctl is-active sshd"
    )
    
    $sshStatus = & ssh @sshStatusArgs 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "SSH daemon is active on remote server"
    } else {
        Write-Warning "SSH daemon status check failed"
    }
} catch {
    Write-Warning "Could not check SSH daemon status"
}

# Check system information on remote server
Write-Info "Checking system information on remote server..."
try {
    # System load
    $loadArgs = @(
        "-i", $SSH_KEY,
        "-o", "ConnectTimeout=10",
        "-o", "StrictHostKeyChecking=no",
        "$EC2_USER@$EC2_HOST",
        "uptime"
    )
    $loadOutput = & ssh @loadArgs 2>$null
    Write-Info "System load: $loadOutput"
    
    # Memory
    $memArgs = @(
        "-i", $SSH_KEY,
        "-o", "ConnectTimeout=10",
        "-o", "StrictHostKeyChecking=no",
        "$EC2_USER@$EC2_HOST",
        "free -h | grep Mem"
    )
    $memOutput = & ssh @memArgs 2>$null
    Write-Info "Memory: $memOutput"
    
    # Disk space
    $diskArgs = @(
        "-i", $SSH_KEY,
        "-o", "ConnectTimeout=10",
        "-o", "StrictHostKeyChecking=no",
        "$EC2_USER@$EC2_HOST",
        "df -h /"
    )
    $diskOutput = & ssh @diskArgs 2>$null
    Write-Info "Disk space: $diskOutput"
    
} catch {
    Write-Warning "Could not retrieve system information"
}

# Test application services
Write-Info "Checking application services..."
try {
    $services = @("changuapp-backend", "nginx", "postgresql")
    $serviceStatus = @{}
    
    foreach ($service in $services) {
        $serviceArgs = @(
            "-i", $SSH_KEY,
            "-o", "ConnectTimeout=10",
            "-o", "StrictHostKeyChecking=no",
            "$EC2_USER@$EC2_HOST",
            "sudo systemctl is-active $service"
        )
        
        $status = & ssh @serviceArgs 2>$null
        if ($LASTEXITCODE -eq 0) {
            $serviceStatus[$service] = $status.Trim()
        } else {
            $serviceStatus[$service] = "inactive"
        }
    }
    
    Write-Host "Service Status:"
    foreach ($service in $serviceStatus.Keys) {
        $status = $serviceStatus[$service]
        $color = if ($status -eq "active") { "Green" } else { "Red" }
        Write-Host "  $service`: $status" -ForegroundColor $color
    }
    
} catch {
    Write-Warning "Could not check service status"
}

# Clean up local SSH connections (Windows equivalent)
Write-Info "Cleaning up local SSH connections..."
try {
    # Kill any SSH processes that might be hung
    Get-Process | Where-Object { $_.ProcessName -eq "ssh" } | ForEach-Object {
        try {
            if ($_.HasExited -eq $false -and $_.Responding -eq $false) {
                Stop-Process -Id $_.Id -Force
                Write-Info "Killed unresponsive SSH process (PID: $($_.Id))"
            }
        } catch {
            # Ignore errors when trying to kill processes
        }
    }
    
    Write-Status "Local SSH cleanup completed"
} catch {
    Write-Warning "Could not perform local SSH cleanup"
}

Write-Host ""
Write-Status "SSH diagnostic completed successfully!"
Write-Host ""
Write-Info "If you're still having issues, try:"
Write-Host "1. Restart the EC2 instance from AWS console"
Write-Host "2. Check AWS CloudWatch logs"
Write-Host "3. Verify security group settings"
Write-Host "4. Contact AWS support if the instance is unresponsive"
Write-Host ""
Write-Info "To kill hung SSH processes automatically, run with -KillHungProcesses parameter"
