"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompleteJobDto = exports.JobCompletionType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var JobCompletionType;
(function (JobCompletionType) {
    JobCompletionType["COMPLETED"] = "completed";
    JobCompletionType["NO_AGREEMENT"] = "no_agreement";
})(JobCompletionType || (exports.JobCompletionType = JobCompletionType = {}));
class CompleteJobDto {
    completedById;
    completionType;
}
exports.CompleteJobDto = CompleteJobDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the user who completed the job',
        required: false,
        example: 'uuid-string'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CompleteJobDto.prototype, "completedById", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of job completion',
        enum: JobCompletionType,
        example: JobCompletionType.COMPLETED
    }),
    (0, class_validator_1.IsEnum)(JobCompletionType),
    __metadata("design:type", String)
], CompleteJobDto.prototype, "completionType", void 0);
//# sourceMappingURL=complete-job.dto.js.map