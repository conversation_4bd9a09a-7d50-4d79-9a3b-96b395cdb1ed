import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Job } from '../jobs/entities/job.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UsersService {
    private readonly userRepository;
    private readonly jobRepository;
    constructor(userRepository: Repository<User>, jobRepository: Repository<Job>);
    create(createUserDto: CreateUserDto): Promise<User>;
    findAll(): Promise<User[]>;
    findOne(id: string): Promise<User>;
    findByFirebaseUid(firebaseUid: string): Promise<User>;
    updateCompletedJobsCount(userId: string): Promise<void>;
    findByEmail(email: string): Promise<User>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<User>;
    remove(id: string): Promise<void>;
    findNearbyUsers(latitude: number, longitude: number, radiusKm?: number): Promise<User[]>;
    toggleVipStatus(id: string): Promise<User>;
}
