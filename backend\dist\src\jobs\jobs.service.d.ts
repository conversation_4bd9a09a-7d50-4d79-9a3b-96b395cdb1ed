import { Repository } from 'typeorm';
import { Job, JobStatus, JobCategory } from './entities/job.entity';
import { JobTemplate } from './entities/job-template.entity';
import { User } from '../users/entities/user.entity';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { CompleteJobDto } from './dto/complete-job.dto';
import { ChatService } from '../chat/chat.service';
export declare class JobsService {
    private readonly jobRepository;
    private readonly jobTemplateRepository;
    private readonly userRepository;
    private readonly chatService;
    constructor(jobRepository: Repository<Job>, jobTemplateRepository: Repository<JobTemplate>, userRepository: Repository<User>, chatService: ChatService);
    create(createJobDto: CreateJobDto, employerId: string): Promise<Job>;
    findAll(page?: number, limit?: number, category?: JobCategory, status?: JobStatus, latitude?: number, longitude?: number, radiusKm?: number, excludeUserId?: string): Promise<{
        jobs: Job[];
        total: number;
        page: number;
        totalPages: number;
    }>;
    findOne(id: string): Promise<Job>;
    findByEmployer(employerId: string): Promise<Job[]>;
    update(id: string, updateJobDto: UpdateJobDto, userId: string): Promise<Job>;
    remove(id: string, userId: string): Promise<void>;
    pauseJob(id: string, userId: string): Promise<Job>;
    resumeJob(id: string, userId: string): Promise<Job>;
    completeJob(id: string, userId: string, completeJobDto?: CompleteJobDto): Promise<Job>;
    findNearbyJobs(latitude: number, longitude: number, radiusKm?: number, category?: JobCategory, excludeUserId?: string): Promise<Job[]>;
    searchJobs(searchTerm: string, category?: JobCategory, minBudget?: number, maxBudget?: number, excludeUserId?: string): Promise<Job[]>;
    private createTemplateFromJob;
    getUserTemplates(userId: string): Promise<JobTemplate[]>;
    getTemplate(templateId: string, userId: string): Promise<JobTemplate>;
    deleteTemplate(templateId: string, userId: string): Promise<void>;
}
