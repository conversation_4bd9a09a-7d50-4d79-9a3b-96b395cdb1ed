"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runMigrations = runMigrations;
const typeorm_1 = require("typeorm");
const config_1 = require("@nestjs/config");
const fs = require("fs");
const path = require("path");
async function runMigrations() {
    const configService = new config_1.ConfigService();
    console.log('🔄 Running database migrations...');
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'postgres123'),
        database: configService.get('DB_DATABASE', 'job_platform'),
        synchronize: false,
        logging: true,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');
        const migrationsDir = path.join(__dirname);
        const migrationFiles = fs.readdirSync(migrationsDir)
            .filter(file => file.endsWith('.sql'))
            .sort();
        console.log(`📁 Found ${migrationFiles.length} migration files`);
        for (const file of migrationFiles) {
            console.log(`🔄 Running migration: ${file}`);
            const migrationPath = path.join(migrationsDir, file);
            const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
            const statements = migrationSQL
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0);
            for (const statement of statements) {
                try {
                    await dataSource.query(statement);
                    console.log(`  ✅ Executed statement successfully`);
                }
                catch (error) {
                    if (error.message.includes('already exists')) {
                        console.log(`  ⚠️  Statement already applied (skipping): ${error.message}`);
                    }
                    else {
                        console.error(`  ❌ Error executing statement:`, error.message);
                        throw error;
                    }
                }
            }
            console.log(`✅ Migration ${file} completed`);
        }
        console.log('🎉 All migrations completed successfully!');
    }
    catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
    finally {
        await dataSource.destroy();
    }
}
if (require.main === module) {
    runMigrations();
}
//# sourceMappingURL=run-migrations.js.map