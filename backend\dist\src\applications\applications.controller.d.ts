import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { Application } from './entities/application.entity';
import { UsersService } from '../users/users.service';
export declare class ApplicationsController {
    private readonly applicationsService;
    private readonly usersService;
    constructor(applicationsService: ApplicationsService, usersService: UsersService);
    create(createApplicationDto: CreateApplicationDto, req: any): Promise<Application>;
    findAll(): Promise<Application[]>;
    getMyApplications(req: any): Promise<Application[]>;
    getJobApplications(jobId: string): Promise<Application[]>;
    findOne(id: string): Promise<Application>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, req: any): Promise<Application>;
    withdraw(id: string, req: any): Promise<Application>;
    remove(id: string, req: any): Promise<void>;
    private getUserFromRequest;
}
