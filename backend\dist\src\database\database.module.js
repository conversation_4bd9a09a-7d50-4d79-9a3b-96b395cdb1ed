"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../users/entities/user.entity");
const job_entity_1 = require("../jobs/entities/job.entity");
const job_template_entity_1 = require("../jobs/entities/job-template.entity");
const application_entity_1 = require("../applications/entities/application.entity");
const contact_request_entity_1 = require("../chat/entities/contact-request.entity");
const conversation_entity_1 = require("../chat/entities/conversation.entity");
const chat_message_entity_1 = require("../chat/entities/chat-message.entity");
const notification_entity_1 = require("../notifications/entities/notification.entity");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'postgres',
                    host: configService.get('DB_HOST') || 'localhost',
                    port: configService.get('DB_PORT') || 5432,
                    username: configService.get('DB_USERNAME') || 'postgres',
                    password: configService.get('DB_PASSWORD') || 'postgres',
                    database: configService.get('DB_DATABASE') || 'job_platform',
                    entities: [user_entity_1.User, job_entity_1.Job, job_template_entity_1.JobTemplate, application_entity_1.Application, contact_request_entity_1.ContactRequest, conversation_entity_1.Conversation, chat_message_entity_1.ChatMessage, notification_entity_1.Notification],
                    synchronize: true,
                    migrations: [],
                    migrationsRun: false,
                    logging: configService.get('NODE_ENV') === 'development',
                    retryAttempts: 3,
                    retryDelay: 3000,
                    ssl: configService.get('NODE_ENV') === 'production' && configService.get('DB_HOST') !== 'postgres' ? { rejectUnauthorized: false } : false,
                    extra: {
                        connectionLimit: 20,
                        acquireTimeout: 60000,
                        timeout: 60000,
                        reconnect: true,
                    },
                    cache: {
                        duration: 30000,
                    },
                    poolSize: 10,
                    maxQueryExecutionTime: 5000,
                }),
                inject: [config_1.ConfigService],
            }),
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map