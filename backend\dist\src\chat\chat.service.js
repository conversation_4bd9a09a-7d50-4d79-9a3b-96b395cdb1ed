"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contact_request_entity_1 = require("./entities/contact-request.entity");
const conversation_entity_1 = require("./entities/conversation.entity");
const chat_message_entity_1 = require("./entities/chat-message.entity");
const job_entity_1 = require("../jobs/entities/job.entity");
const user_entity_1 = require("../users/entities/user.entity");
const notifications_service_1 = require("../notifications/notifications.service");
const notification_entity_1 = require("../notifications/entities/notification.entity");
let ChatService = class ChatService {
    contactRequestRepository;
    conversationRepository;
    chatMessageRepository;
    jobRepository;
    userRepository;
    notificationsService;
    constructor(contactRequestRepository, conversationRepository, chatMessageRepository, jobRepository, userRepository, notificationsService) {
        this.contactRequestRepository = contactRequestRepository;
        this.conversationRepository = conversationRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.jobRepository = jobRepository;
        this.userRepository = userRepository;
        this.notificationsService = notificationsService;
    }
    async createContactRequest(userId, createContactRequestDto) {
        try {
            const { jobId, message } = createContactRequestDto;
            console.log('🔍 Creating contact request:', { userId, jobId, message });
            const job = await this.jobRepository.findOne({
                where: { id: jobId },
                relations: ['employer']
            });
            if (!job) {
                console.error('❌ Job not found:', jobId);
                throw new common_1.NotFoundException('Job not found');
            }
            console.log('✅ Job found:', { jobId: job.id, employerId: job.employer?.id });
            if (job.employer?.id === userId) {
                console.error('❌ User trying to contact themselves:', { userId, employerId: job.employer.id });
                throw new common_1.BadRequestException('Cannot send contact request to yourself');
            }
            const existingRequest = await this.contactRequestRepository.findOne({
                where: {
                    requesterId: userId,
                    jobId: jobId,
                    status: contact_request_entity_1.ContactRequestStatus.PENDING
                }
            });
            if (existingRequest) {
                console.error('❌ Contact request already exists:', { userId, jobId });
                throw new common_1.BadRequestException('Contact request already sent for this job');
            }
            const contactRequest = this.contactRequestRepository.create({
                requesterId: userId,
                jobPosterId: job.employer.id,
                jobId: jobId,
                message: message || 'Me interesa este trabajo',
                status: contact_request_entity_1.ContactRequestStatus.PENDING
            });
            console.log('💾 Saving contact request:', contactRequest);
            const savedRequest = await this.contactRequestRepository.save(contactRequest);
            console.log('✅ Contact request saved:', savedRequest.id);
            try {
                await this.notificationsService.create({
                    type: notification_entity_1.NotificationType.CONTACT_REQUEST,
                    title: 'Nueva solicitud de contacto',
                    message: `${savedRequest.requester?.firstName || 'Alguien'} está interesado en tu trabajo: ${job.title}`,
                    userId: job.employer.id,
                    relatedEntityId: savedRequest.id,
                    relatedEntityType: 'contact_request',
                    metadata: {
                        jobId: job.id,
                        jobTitle: job.title,
                        requesterId: userId
                    }
                });
                console.log('✅ Contact request notification created');
            }
            catch (notificationError) {
                console.error('❌ Error creating contact request notification:', notificationError);
            }
            return savedRequest;
        }
        catch (error) {
            console.error('❌ Error in createContactRequest:', error);
            throw error;
        }
    }
    async getContactRequests(userId) {
        return await this.contactRequestRepository
            .createQueryBuilder('contactRequest')
            .leftJoinAndSelect('contactRequest.requester', 'requester')
            .innerJoinAndSelect('contactRequest.job', 'job')
            .leftJoinAndSelect('contactRequest.jobPoster', 'jobPoster')
            .where('contactRequest.jobPosterId = :userId', { userId })
            .andWhere('contactRequest.status = :status', { status: contact_request_entity_1.ContactRequestStatus.PENDING })
            .andWhere('job.deletedAt IS NULL')
            .orderBy('contactRequest.createdAt', 'DESC')
            .getMany();
    }
    async getContactRequestStatus(userId, jobId) {
        const existingRequest = await this.contactRequestRepository
            .createQueryBuilder('contactRequest')
            .leftJoinAndSelect('contactRequest.job', 'job')
            .where('contactRequest.requesterId = :userId', { userId })
            .andWhere('contactRequest.jobId = :jobId', { jobId })
            .andWhere('job.deletedAt IS NULL')
            .orderBy('contactRequest.createdAt', 'DESC')
            .getOne();
        if (!existingRequest) {
            return { hasContactRequest: false };
        }
        return {
            hasContactRequest: true,
            status: existingRequest.status,
            requestId: existingRequest.id
        };
    }
    async respondToContactRequest(userId, requestId, respondDto) {
        const contactRequest = await this.contactRequestRepository.findOne({
            where: { id: requestId }
        });
        if (!contactRequest) {
            throw new common_1.NotFoundException('Contact request not found');
        }
        if (contactRequest.jobPosterId !== userId) {
            throw new common_1.ForbiddenException('You can only respond to your own job contact requests');
        }
        if (contactRequest.status !== contact_request_entity_1.ContactRequestStatus.PENDING) {
            throw new common_1.BadRequestException('Contact request has already been responded to');
        }
        contactRequest.status = respondDto.status;
        const updatedRequest = await this.contactRequestRepository.save(contactRequest);
        if (respondDto.status === contact_request_entity_1.ContactRequestStatus.ACCEPTED) {
            await this.createConversation(contactRequest);
            try {
                await this.notificationsService.create({
                    type: notification_entity_1.NotificationType.CONTACT_REQUEST,
                    title: 'Solicitud aceptada',
                    message: `Tu solicitud para el trabajo "${contactRequest.job?.title || 'trabajo'}" ha sido aceptada. ¡Ya puedes chatear!`,
                    userId: contactRequest.requesterId,
                    relatedEntityId: contactRequest.id,
                    relatedEntityType: 'contact_request',
                    metadata: {
                        jobId: contactRequest.jobId,
                        jobTitle: contactRequest.job?.title,
                        status: 'accepted'
                    }
                });
                console.log('✅ Contact request acceptance notification created');
            }
            catch (notificationError) {
                console.error('❌ Error creating acceptance notification:', notificationError);
            }
        }
        return updatedRequest;
    }
    async createConversation(contactRequest) {
        const existingConversation = await this.conversationRepository.findOne({
            where: [
                { user1Id: contactRequest.requesterId, user2Id: contactRequest.jobPosterId, jobId: contactRequest.jobId },
                { user1Id: contactRequest.jobPosterId, user2Id: contactRequest.requesterId, jobId: contactRequest.jobId }
            ]
        });
        if (existingConversation) {
            return existingConversation;
        }
        const conversation = this.conversationRepository.create({
            user1Id: contactRequest.requesterId,
            user2Id: contactRequest.jobPosterId,
            jobId: contactRequest.jobId,
            lastMessageAt: new Date(),
            lastMessagePreview: 'Conversación iniciada'
        });
        const savedConversation = await this.conversationRepository.save(conversation);
        const systemMessage = this.chatMessageRepository.create({
            conversationId: savedConversation.id,
            senderId: contactRequest.jobPosterId,
            content: 'Conversación iniciada. ¡Pueden empezar a chatear!',
            type: chat_message_entity_1.MessageType.SYSTEM,
            isRead: false
        });
        await this.chatMessageRepository.save(systemMessage);
        return savedConversation;
    }
    async getConversations(userId) {
        return await this.conversationRepository
            .createQueryBuilder('conversation')
            .leftJoinAndSelect('conversation.user1', 'user1')
            .leftJoinAndSelect('conversation.user2', 'user2')
            .leftJoinAndSelect('conversation.job', 'job')
            .where('(conversation.user1Id = :userId OR conversation.user2Id = :userId)', { userId })
            .andWhere('conversation.isClosed = :isClosed', { isClosed: false })
            .orderBy('conversation.lastMessageAt', 'DESC')
            .getMany();
    }
    async getMessages(userId, conversationId, page = 1, limit = 50) {
        const conversation = await this.conversationRepository.findOne({
            where: { id: conversationId }
        });
        if (!conversation) {
            throw new common_1.NotFoundException('Conversation not found');
        }
        if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
            throw new common_1.ForbiddenException('You are not part of this conversation');
        }
        const messages = await this.chatMessageRepository.find({
            where: { conversationId },
            order: { createdAt: 'DESC' },
            skip: (page - 1) * limit,
            take: limit
        });
        return messages.reverse();
    }
    async sendMessage(userId, sendMessageDto) {
        const { conversationId, content } = sendMessageDto;
        const conversation = await this.conversationRepository.findOne({
            where: { id: conversationId }
        });
        if (!conversation) {
            throw new common_1.NotFoundException('Conversation not found');
        }
        if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
            throw new common_1.ForbiddenException('You are not part of this conversation');
        }
        const message = this.chatMessageRepository.create({
            conversationId,
            senderId: userId,
            content,
            type: chat_message_entity_1.MessageType.TEXT,
            isRead: false
        });
        const savedMessage = await this.chatMessageRepository.save(message);
        const otherUserId = conversation.user1Id === userId ? conversation.user2Id : conversation.user1Id;
        if (conversation.user1Id === otherUserId) {
            conversation.unreadCountUser1 += 1;
        }
        else {
            conversation.unreadCountUser2 += 1;
        }
        conversation.lastMessageAt = new Date();
        conversation.lastMessagePreview = content.substring(0, 100);
        await this.conversationRepository.save(conversation);
        try {
            const sender = await this.userRepository.findOne({ where: { id: userId } });
            await this.notificationsService.create({
                type: notification_entity_1.NotificationType.MESSAGE,
                title: 'Nuevo mensaje',
                message: `${sender?.firstName || 'Alguien'} te envió un mensaje: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
                userId: otherUserId,
                relatedEntityId: conversation.id,
                relatedEntityType: 'conversation',
                metadata: {
                    conversationId: conversation.id,
                    senderId: userId,
                    jobId: conversation.jobId,
                    jobTitle: conversation.job?.title
                }
            });
            console.log('✅ Message notification created');
        }
        catch (notificationError) {
            console.error('❌ Error creating message notification:', notificationError);
        }
        return savedMessage;
    }
    async markMessagesAsRead(userId, conversationId) {
        const conversation = await this.conversationRepository.findOne({
            where: { id: conversationId }
        });
        if (!conversation) {
            throw new common_1.NotFoundException('Conversation not found');
        }
        if (conversation.user1Id !== userId && conversation.user2Id !== userId) {
            throw new common_1.ForbiddenException('You are not part of this conversation');
        }
        await this.chatMessageRepository.update({ conversationId, isRead: false }, { isRead: true });
        const unreadField = conversation.user1Id === userId ? 'unreadCountUser1' : 'unreadCountUser2';
        await this.conversationRepository.update(conversationId, {
            [unreadField]: 0
        });
    }
    async findUserByFirebaseUid(firebaseUid) {
        try {
            return await this.userRepository.findOne({ where: { firebaseUid } });
        }
        catch (error) {
            console.error('Error finding user by firebaseUid:', error);
            return null;
        }
    }
    async getAcceptedContactRequestsForJob(jobId, userId) {
        const job = await this.jobRepository.findOne({
            where: { id: jobId },
            relations: ['employer'],
            withDeleted: false
        });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only view contact requests for your own jobs');
        }
        return await this.contactRequestRepository
            .createQueryBuilder('contactRequest')
            .leftJoinAndSelect('contactRequest.requester', 'requester')
            .leftJoinAndSelect('contactRequest.job', 'job')
            .where('contactRequest.jobId = :jobId', { jobId })
            .andWhere('contactRequest.status = :status', { status: contact_request_entity_1.ContactRequestStatus.ACCEPTED })
            .andWhere('job.deletedAt IS NULL')
            .orderBy('contactRequest.createdAt', 'DESC')
            .getMany();
    }
    async getContactRequestCountForJob(jobId, userId) {
        const job = await this.jobRepository.findOne({
            where: { id: jobId },
            relations: ['employer'],
            withDeleted: false
        });
        if (!job) {
            throw new common_1.NotFoundException('Job not found');
        }
        if (job.employerId !== userId) {
            throw new common_1.ForbiddenException('You can only view contact requests for your own jobs');
        }
        return await this.contactRequestRepository
            .createQueryBuilder('contactRequest')
            .leftJoinAndSelect('contactRequest.job', 'job')
            .where('contactRequest.jobId = :jobId', { jobId })
            .andWhere('job.deletedAt IS NULL')
            .getCount();
    }
    async getContactRequestCountsForJobs(jobIds, userId) {
        if (jobIds.length === 0) {
            return new Map();
        }
        const counts = await this.contactRequestRepository
            .createQueryBuilder('contactRequest')
            .leftJoinAndSelect('contactRequest.job', 'job')
            .select('contactRequest.jobId', 'jobId')
            .addSelect('COUNT(*)', 'count')
            .where('contactRequest.jobId IN (:...jobIds)', { jobIds })
            .andWhere('job.deletedAt IS NULL')
            .andWhere('job.employerId = :userId', { userId })
            .groupBy('contactRequest.jobId')
            .getRawMany();
        const countMap = new Map();
        jobIds.forEach(jobId => countMap.set(jobId, 0));
        counts.forEach(row => {
            countMap.set(row.jobId, parseInt(row.count, 10));
        });
        return countMap;
    }
    async closeConversation(conversationId, userId) {
        const conversation = await this.conversationRepository.findOne({
            where: { id: conversationId },
            relations: ['job']
        });
        if (!conversation) {
            throw new common_1.NotFoundException('Conversation not found');
        }
        if (conversation.job.employerId !== userId) {
            throw new common_1.ForbiddenException('Only the job poster can close conversations');
        }
        if (conversation.isClosed) {
            throw new common_1.BadRequestException('Conversation is already closed');
        }
        conversation.isClosed = true;
        conversation.closedByUserId = userId;
        conversation.closedAt = new Date();
        const systemMessage = this.chatMessageRepository.create({
            conversationId: conversation.id,
            senderId: userId,
            content: 'El empleador ha cerrado esta conversación.',
            type: chat_message_entity_1.MessageType.SYSTEM,
            isRead: false
        });
        await this.chatMessageRepository.save(systemMessage);
        return await this.conversationRepository.save(conversation);
    }
};
exports.ChatService = ChatService;
exports.ChatService = ChatService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contact_request_entity_1.ContactRequest)),
    __param(1, (0, typeorm_1.InjectRepository)(conversation_entity_1.Conversation)),
    __param(2, (0, typeorm_1.InjectRepository)(chat_message_entity_1.ChatMessage)),
    __param(3, (0, typeorm_1.InjectRepository)(job_entity_1.Job)),
    __param(4, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        notifications_service_1.NotificationsService])
], ChatService);
//# sourceMappingURL=chat.service.js.map