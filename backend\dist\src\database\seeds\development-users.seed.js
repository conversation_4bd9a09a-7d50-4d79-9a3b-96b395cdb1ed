"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedDevelopmentUsers = seedDevelopmentUsers;
const user_entity_1 = require("../../users/entities/user.entity");
async function seedDevelopmentUsers(dataSource) {
    const userRepository = dataSource.getRepository(user_entity_1.User);
    const existingUser = await userRepository.findOne({ where: { firebaseUid: 'user1' } });
    if (existingUser) {
        console.log('✅ Development user already exists');
        return;
    }
    const developmentUser = userRepository.create({
        email: '<EMAIL>',
        firebaseUid: 'user1',
        firstName: 'Dev',
        lastName: 'User',
        phone: '+1234567890',
        role: user_entity_1.UserRole.BOTH,
        bio: 'Development user for testing purposes',
        skills: 'Testing, Development, Debugging',
        location: 'Córdoba, Argentina',
        latitude: -31.857050,
        longitude: -64.982221,
        hourlyRate: 2500,
        isAvailable: true,
        isVip: false,
        completedJobs: 5,
    });
    await userRepository.save(developmentUser);
    console.log('✅ Development user created successfully');
    const existingUser2 = await userRepository.findOne({ where: { firebaseUid: 'user2' } });
    if (!existingUser2) {
        const developmentUser2 = userRepository.create({
            email: '<EMAIL>',
            firebaseUid: 'user2',
            firstName: 'Test',
            lastName: 'Employer',
            phone: '+1234567891',
            role: user_entity_1.UserRole.EMPLOYER,
            bio: 'Test employer for development',
            skills: 'Management, Hiring',
            location: 'Villa Crespo, Buenos Aires',
            latitude: -34.6037,
            longitude: -58.3816,
            hourlyRate: 3000,
            isAvailable: true,
            isVip: true,
            completedJobs: 10,
        });
        await userRepository.save(developmentUser2);
        console.log('✅ Second development user created successfully');
    }
}
//# sourceMappingURL=development-users.seed.js.map