import { User } from '../../users/entities/user.entity';
import { JobCategory, PaymentMethod } from './job.entity';
export declare class JobTemplate {
    id: string;
    title: string;
    description: string;
    category: JobCategory;
    budget: number;
    location: string;
    latitude: number;
    longitude: number;
    requiredSkills?: string;
    estimatedHours?: number;
    isUrgent: boolean;
    images?: string;
    icon?: string;
    paymentMethods: PaymentMethod[];
    createdAt: Date;
    user: User;
    userId: string;
}
