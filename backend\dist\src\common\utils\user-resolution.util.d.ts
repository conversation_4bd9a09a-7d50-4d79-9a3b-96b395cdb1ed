import { UsersService } from '../../users/users.service';
import { User } from '../../users/entities/user.entity';
export interface AuthenticatedRequest {
    user?: {
        sub?: string;
        uid?: string;
        firebaseUid?: string;
        email?: string;
        emailVerified?: boolean;
        name?: string;
    };
}
export declare class UserResolutionUtil {
    private readonly usersService;
    constructor(usersService: UsersService);
    getUserFromRequest(req: AuthenticatedRequest): Promise<User>;
    getUserIdFromRequest(req: AuthenticatedRequest): Promise<string>;
    getUserIdFromRequestSafe(req: AuthenticatedRequest): Promise<string | null>;
    isAuthenticated(req: AuthenticatedRequest): boolean;
    getFallbackUser(fallbackFirebaseUid?: string): Promise<User | null>;
}
