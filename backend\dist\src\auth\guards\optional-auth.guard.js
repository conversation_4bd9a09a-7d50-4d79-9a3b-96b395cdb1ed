"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OptionalAuthGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptionalAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const admin = require("firebase-admin");
let OptionalAuthGuard = OptionalAuthGuard_1 = class OptionalAuthGuard {
    jwtService;
    configService;
    logger = new common_1.Logger(OptionalAuthGuard_1.name);
    constructor(jwtService, configService) {
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            this.logger.debug('No token provided - allowing request without authentication');
            return true;
        }
        try {
            const secret = this.configService.get('JWT_SECRET', 'dev-secret-key');
            const payload = this.jwtService.verify(token, { secret });
            request.user = {
                sub: payload.sub,
                uid: payload.firebaseUid,
                email: payload.email,
                firebaseUid: payload.firebaseUid,
            };
            this.logger.debug(`JWT token validated for user: ${payload.sub}`);
            return true;
        }
        catch (jwtError) {
            try {
                const decodedToken = await admin.auth().verifyIdToken(token);
                request.user = {
                    uid: decodedToken.uid,
                    firebaseUid: decodedToken.uid,
                    email: decodedToken.email,
                    emailVerified: decodedToken.email_verified,
                    name: decodedToken.name,
                };
                this.logger.debug(`Firebase token validated for user: ${decodedToken.uid}`);
                return true;
            }
            catch (firebaseError) {
                this.logger.warn(`Invalid token provided - JWT: ${jwtError.message}, Firebase: ${firebaseError.message}`);
                return true;
            }
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type?.toLowerCase() === 'bearer' ? token : undefined;
    }
};
exports.OptionalAuthGuard = OptionalAuthGuard;
exports.OptionalAuthGuard = OptionalAuthGuard = OptionalAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService])
], OptionalAuthGuard);
//# sourceMappingURL=optional-auth.guard.js.map