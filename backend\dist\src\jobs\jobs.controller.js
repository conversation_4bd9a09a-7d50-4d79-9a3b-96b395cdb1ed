"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jobs_service_1 = require("./jobs.service");
const create_job_dto_1 = require("./dto/create-job.dto");
const update_job_dto_1 = require("./dto/update-job.dto");
const complete_job_dto_1 = require("./dto/complete-job.dto");
const job_entity_1 = require("./entities/job.entity");
const job_template_entity_1 = require("./entities/job-template.entity");
const optional_auth_guard_1 = require("../auth/guards/optional-auth.guard");
const users_service_1 = require("../users/users.service");
const user_resolution_util_1 = require("../common/utils/user-resolution.util");
const chat_service_1 = require("../chat/chat.service");
let JobsController = class JobsController {
    jobsService;
    usersService;
    userResolutionUtil;
    chatService;
    constructor(jobsService, usersService, userResolutionUtil, chatService) {
        this.jobsService = jobsService;
        this.usersService = usersService;
        this.userResolutionUtil = userResolutionUtil;
        this.chatService = chatService;
    }
    async create(createJobDto, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.create(createJobDto, user.id);
    }
    async findAll(page, limit, category, status, latitude, longitude, radiusKm, excludeUserId, req) {
        if (!excludeUserId) {
            try {
                if (req?.user?.uid) {
                    const user = await this.getUserFromRequest(req);
                    excludeUserId = user.id;
                }
            }
            catch (error) {
                excludeUserId = undefined;
            }
        }
        return this.jobsService.findAll(page, limit, category, status, latitude, longitude, radiusKm, excludeUserId);
    }
    async search(searchTerm, category, minBudget, maxBudget, excludeUserId, req) {
        if (!excludeUserId) {
            try {
                if (req?.user?.uid) {
                    const user = await this.getUserFromRequest(req);
                    excludeUserId = user.id;
                }
            }
            catch (error) {
                excludeUserId = undefined;
            }
        }
        return this.jobsService.searchJobs(searchTerm, category, minBudget, maxBudget, excludeUserId);
    }
    findNearby(latitude, longitude, radius, category, excludeUserId) {
        return this.jobsService.findNearbyJobs(latitude, longitude, radius, category, excludeUserId);
    }
    async getMyJobs(req) {
        const user = await this.getUserFromRequest(req);
        const jobs = await this.jobsService.findByEmployer(user.id);
        const jobIds = jobs.map(job => job.id);
        const contactRequestCounts = await this.chatService.getContactRequestCountsForJobs(jobIds, user.id);
        const jobsWithCounts = jobs.map(job => ({
            ...job,
            applicationsCount: contactRequestCounts.get(job.id) || 0,
        }));
        return jobsWithCounts;
    }
    findOne(id) {
        return this.jobsService.findOne(id);
    }
    async update(id, updateJobDto, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.update(id, updateJobDto, user.id);
    }
    async remove(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.remove(id, user.id);
    }
    async pauseJob(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.pauseJob(id, user.id);
    }
    async resumeJob(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.resumeJob(id, user.id);
    }
    async completeJob(id, completeJobDto, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.completeJob(id, user.id, completeJobDto);
    }
    async getMyTemplates(req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.getUserTemplates(user.id);
    }
    async getTemplate(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.getTemplate(id, user.id);
    }
    async deleteTemplate(id, req) {
        const user = await this.getUserFromRequest(req);
        return this.jobsService.deleteTemplate(id, user.id);
    }
    async getUserFromRequest(req) {
        return await this.userResolutionUtil.getUserFromRequest(req);
    }
};
exports.JobsController = JobsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new job posting' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Job created successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_job_dto_1.CreateJobDto, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all jobs with pagination and filters' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: 'number', description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: 'number', description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, enum: job_entity_1.JobCategory, description: 'Filter by category' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: job_entity_1.JobStatus, description: 'Filter by status' }),
    (0, swagger_1.ApiQuery)({ name: 'latitude', required: false, type: 'number', description: 'Latitude for location-based search' }),
    (0, swagger_1.ApiQuery)({ name: 'longitude', required: false, type: 'number', description: 'Longitude for location-based search' }),
    (0, swagger_1.ApiQuery)({ name: 'radiusKm', required: false, type: 'number', description: 'Search radius in kilometers' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeUserId', required: false, type: 'string', description: 'User ID to exclude from results' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Jobs retrieved successfully' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('category')),
    __param(3, (0, common_1.Query)('status')),
    __param(4, (0, common_1.Query)('latitude')),
    __param(5, (0, common_1.Query)('longitude')),
    __param(6, (0, common_1.Query)('radiusKm')),
    __param(7, (0, common_1.Query)('excludeUserId')),
    __param(8, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, Number, Number, Number, String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Search jobs by keyword and filters' }),
    (0, swagger_1.ApiQuery)({ name: 'q', type: 'string', description: 'Search term' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, enum: job_entity_1.JobCategory, description: 'Filter by category' }),
    (0, swagger_1.ApiQuery)({ name: 'minBudget', required: false, type: 'number', description: 'Minimum budget' }),
    (0, swagger_1.ApiQuery)({ name: 'maxBudget', required: false, type: 'number', description: 'Maximum budget' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeUserId', required: false, type: 'string', description: 'User ID to exclude from results' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Search results retrieved successfully', type: [job_entity_1.Job] }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Query)('minBudget')),
    __param(3, (0, common_1.Query)('maxBudget')),
    __param(4, (0, common_1.Query)('excludeUserId')),
    __param(5, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Number, String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('nearby'),
    (0, swagger_1.ApiOperation)({ summary: 'Find nearby jobs based on location' }),
    (0, swagger_1.ApiQuery)({ name: 'latitude', type: 'number', description: 'Latitude coordinate' }),
    (0, swagger_1.ApiQuery)({ name: 'longitude', type: 'number', description: 'Longitude coordinate' }),
    (0, swagger_1.ApiQuery)({ name: 'radius', type: 'number', required: false, description: 'Search radius in kilometers (default: 50)' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, enum: job_entity_1.JobCategory, description: 'Filter by category' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeUserId', required: false, type: 'string', description: 'User ID to exclude from results' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Nearby jobs retrieved successfully', type: [job_entity_1.Job] }),
    __param(0, (0, common_1.Query)('latitude')),
    __param(1, (0, common_1.Query)('longitude')),
    __param(2, (0, common_1.Query)('radius')),
    __param(3, (0, common_1.Query)('category')),
    __param(4, (0, common_1.Query)('excludeUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, String, String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findNearby", null);
__decorate([
    (0, common_1.Get)('my-jobs'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user posted jobs' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User jobs retrieved successfully', type: [job_entity_1.Job] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "getMyJobs", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job retrieved successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update job by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job updated successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_job_dto_1.UpdateJobDto, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete job by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/pause'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Pause job by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job paused successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "pauseJob", null);
__decorate([
    (0, common_1.Patch)(':id/resume'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Resume job by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job resumed successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "resumeJob", null);
__decorate([
    (0, common_1.Patch)(':id/complete'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Mark job as completed by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job marked as completed successfully', type: job_entity_1.Job }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not job owner' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, complete_job_dto_1.CompleteJobDto, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "completeJob", null);
__decorate([
    (0, common_1.Get)('templates/my'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get user job templates' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Templates retrieved successfully', type: [job_template_entity_1.JobTemplate] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "getMyTemplates", null);
__decorate([
    (0, common_1.Get)('templates/:id'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get template by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template retrieved successfully', type: job_template_entity_1.JobTemplate }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "getTemplate", null);
__decorate([
    (0, common_1.Delete)('templates/:id'),
    (0, common_1.UseGuards)(optional_auth_guard_1.OptionalAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete template by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "deleteTemplate", null);
exports.JobsController = JobsController = __decorate([
    (0, swagger_1.ApiTags)('jobs'),
    (0, common_1.Controller)('jobs'),
    __metadata("design:paramtypes", [jobs_service_1.JobsService,
        users_service_1.UsersService,
        user_resolution_util_1.UserResolutionUtil,
        chat_service_1.ChatService])
], JobsController);
//# sourceMappingURL=jobs.controller.js.map