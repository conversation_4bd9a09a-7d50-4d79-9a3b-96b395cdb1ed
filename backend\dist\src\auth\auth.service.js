"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const firebase_config_1 = require("../config/firebase.config");
let AuthService = class AuthService {
    jwtService;
    usersService;
    constructor(jwtService, usersService) {
        this.jwtService = jwtService;
        this.usersService = usersService;
    }
    async validateFirebaseToken(token) {
        try {
            if (!firebase_config_1.firebaseAdmin.apps.length) {
                throw new Error('Firebase Admin SDK not initialized');
            }
            const decodedToken = await firebase_config_1.firebaseAdmin.auth().verifyIdToken(token);
            return {
                uid: decodedToken.uid,
                email: decodedToken.email,
                emailVerified: decodedToken.email_verified,
                name: decodedToken.name,
            };
        }
        catch (error) {
            console.error('Firebase token validation error:', error);
            throw new common_1.UnauthorizedException('Invalid Firebase token');
        }
    }
    async login(firebaseToken) {
        const firebaseUser = await this.validateFirebaseToken(firebaseToken);
        try {
            const user = await this.usersService.findByFirebaseUid(firebaseUser.uid);
            const payload = {
                sub: user.id,
                email: user.email,
                firebaseUid: user.firebaseUid
            };
            return {
                accessToken: this.jwtService.sign(payload),
                user,
            };
        }
        catch (error) {
            try {
                const userByEmail = await this.usersService.findByEmail(firebaseUser.email);
                if (userByEmail && !userByEmail.firebaseUid) {
                    const updatedUser = await this.usersService.update(userByEmail.id, {
                        firebaseUid: firebaseUser.uid
                    });
                    const payload = {
                        sub: updatedUser.id,
                        email: updatedUser.email,
                        firebaseUid: updatedUser.firebaseUid
                    };
                    return {
                        accessToken: this.jwtService.sign(payload),
                        user: updatedUser,
                    };
                }
            }
            catch (emailError) {
            }
            throw new common_1.UnauthorizedException('User not found. Please register first.');
        }
    }
    async register(firebaseToken, userData) {
        const firebaseUser = await this.validateFirebaseToken(firebaseToken);
        try {
            const existingUser = await this.usersService.findByFirebaseUid(firebaseUser.uid);
            if (existingUser) {
                const payload = {
                    sub: existingUser.id,
                    email: existingUser.email,
                    firebaseUid: existingUser.firebaseUid
                };
                return {
                    accessToken: this.jwtService.sign(payload),
                    user: existingUser,
                };
            }
        }
        catch (error) {
        }
        try {
            const userByEmail = await this.usersService.findByEmail(firebaseUser.email);
            if (userByEmail) {
                const updatedUser = await this.usersService.update(userByEmail.id, {
                    firebaseUid: firebaseUser.uid,
                    firstName: userData.firstName || userByEmail.firstName,
                    lastName: userData.lastName || userByEmail.lastName,
                    phone: userData.phone || userByEmail.phone,
                });
                const payload = {
                    sub: updatedUser.id,
                    email: updatedUser.email,
                    firebaseUid: updatedUser.firebaseUid
                };
                return {
                    accessToken: this.jwtService.sign(payload),
                    user: updatedUser,
                };
            }
        }
        catch (emailError) {
        }
        const [firstName, lastName] = firebaseUser.name
            ? firebaseUser.name.split(' ', 2)
            : [userData.firstName || '', userData.lastName || ''];
        const newUser = await this.usersService.create({
            email: firebaseUser.email,
            firebaseUid: firebaseUser.uid,
            firstName: firstName || userData.firstName,
            lastName: lastName || userData.lastName,
            phone: userData.phone,
        });
        const payload = {
            sub: newUser.id,
            email: newUser.email,
            firebaseUid: newUser.firebaseUid
        };
        return {
            accessToken: this.jwtService.sign(payload),
            user: newUser,
        };
    }
    async getCurrentUser(firebaseUid) {
        return await this.usersService.findByFirebaseUid(firebaseUid);
    }
    async recoverFirebaseBackendSync(firebaseToken, userData) {
        const firebaseUser = await this.validateFirebaseToken(firebaseToken);
        try {
            const existingUser = await this.usersService.findByFirebaseUid(firebaseUser.uid);
            if (existingUser) {
                const payload = {
                    sub: existingUser.id,
                    email: existingUser.email,
                    firebaseUid: existingUser.firebaseUid
                };
                return {
                    accessToken: this.jwtService.sign(payload),
                    user: existingUser,
                };
            }
        }
        catch (error) {
        }
        try {
            const userByEmail = await this.usersService.findByEmail(firebaseUser.email);
            if (userByEmail) {
                const updatedUser = await this.usersService.update(userByEmail.id, {
                    firebaseUid: firebaseUser.uid,
                });
                const payload = {
                    sub: updatedUser.id,
                    email: updatedUser.email,
                    firebaseUid: updatedUser.firebaseUid
                };
                return {
                    accessToken: this.jwtService.sign(payload),
                    user: updatedUser,
                };
            }
        }
        catch (emailError) {
        }
        const [firstName, lastName] = firebaseUser.name
            ? firebaseUser.name.split(' ', 2)
            : [userData?.firstName || '', userData?.lastName || ''];
        const newUser = await this.usersService.create({
            email: firebaseUser.email,
            firebaseUid: firebaseUser.uid,
            firstName: firstName || userData?.firstName || 'Usuario',
            lastName: lastName || userData?.lastName || '',
            phone: userData?.phone || '',
        });
        const payload = {
            sub: newUser.id,
            email: newUser.email,
            firebaseUid: newUser.firebaseUid
        };
        return {
            accessToken: this.jwtService.sign(payload),
            user: newUser,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        users_service_1.UsersService])
], AuthService);
//# sourceMappingURL=auth.service.js.map