import { JobCategory, PaymentMethod } from '../entities/job.entity';
export declare class CreateJobDto {
    title: string;
    description: string;
    category: JobCategory;
    budget: number;
    location: string;
    latitude: number;
    longitude: number;
    requiredSkills?: string;
    estimatedHours?: number;
    startDate?: string;
    deadline?: string;
    isUrgent?: boolean;
    images?: string;
    icon?: string;
    paymentMethods: PaymentMethod[];
}
