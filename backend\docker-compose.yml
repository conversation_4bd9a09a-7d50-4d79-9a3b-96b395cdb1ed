services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: job-platform-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: job_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d job_platform"]
      interval: 10s
      timeout: 5s
      retries: 5

  # NestJS Backend API with PostgreSQL
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: job-platform-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: postgres123
      DB_DATABASE: job_platform
      DATABASE_URL: ***********************************************/job_platform
      JWT_SECRET: ChanguApp2024SuperSecretJWTKeyChangeInProduction
      JWT_EXPIRES_IN: "24h"
      FIREBASE_PROJECT_ID: tu-changa-583b3
      CORS_ORIGIN: "*"
      API_PREFIX: ""
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - job-platform-network
    healthcheck:
      test: ["CMD", "node", "/app/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:

networks:
  job-platform-network:
    driver: bridge
