version: '3.8'

services:
  # PostgreSQL Database - Production Configuration
  postgres:
    environment:
      POSTGRES_DB: job_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ChanguApp2024SecureDB
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"  # Explicitly expose port for production
    restart: unless-stopped

  # NestJS Backend API - Production Configuration
  backend:
    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: ChanguApp2024SecureDB
      DB_DATABASE: job_platform
      DATABASE_URL: *********************************************************/job_platform
      JWT_SECRET: ChanguApp2024SuperSecretJWTKeyChangeInProduction
      JWT_EXPIRES_IN: "24h"
      FIREBASE_PROJECT_ID: tu-changa-583b3
      CORS_ORIGIN: "*"
      API_PREFIX: ""
    ports:
      - "3000:3000"  # Explicitly expose port for production
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  job-platform-network:
    driver: bridge
